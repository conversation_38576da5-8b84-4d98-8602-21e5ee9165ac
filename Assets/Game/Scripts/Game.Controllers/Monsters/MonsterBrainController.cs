using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Levels;
using Game.Views.Monsters;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using Monsters_MonsterDamageArgs = Game.Views.Monsters.MonsterDamageArgs;
using Random = UnityEngine.Random;

namespace Game.Controllers.Monsters
{
    public class MonsterBrainController : NetworkActor
    {
        [SerializeField] private MonsterActor monster;
        [SerializeField] private NavMeshAgent navMeshAgent;

        private LevelModel levelModel;
        private MonstersConfig monstersConfig;
        private PlayersModel playersModel;
        private IAudioClient audioClient;
        private float nextRotateToTargetTime;
        private float nextDamageReceiveTime;
        private float nextVoiceTime;
        private float nextAttackTime;

        private bool HasTarget => TargetPlayer != null;
        private bool IsAttacking => Time.time < nextAttackTime;
        private bool IsDamageReceiving => Time.time < nextDamageReceiveTime;
        private bool IsAlive => monster.IsAlive;
        private float ChaseSpeed => monster.ChaseSpeed;
        private byte AttackDamage => monster.AttackDamage;
        private float PatrolSpeed => monster.PatrolSpeed;
        private bool isStuck;
        private float nextPlayerCheckTime;
        private bool isTransferringAuthority;
        private float nextPathUpdateTime;
        private const float pathUpdateInterval = 0.2f;

        private float nextTargetSwitchTime;
        private const float targetSwitchInterval = 5f;

        private float patrolBoundsXMin, patrolBoundsXMax, patrolBoundsZMin, patrolBoundsZMax, patrolBoundsYMin, patrolBoundsYMax;
        private bool hasPatrolArea;

        [Networked] [OnChangedRender(nameof(HandleTargetPlayerChanged))]
        private PlayerActor TargetPlayer { get; set; }

        private MonsterState State
        {
            get => monster.State;
            set
            {
                previousFrameState = monster.State;
                monster.State = value;
            }
        }

        private MonsterState previousFrameState;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            MonstersConfig monstersConfig,
            PlayersModel playersModel,
            ISubscriber<Monsters_MonsterDamageArgs> damageSubscriber,
            IAudioClient audioClient)
        {
            this.levelModel = levelModel;
            this.monstersConfig = monstersConfig;
            this.playersModel = playersModel;
            this.audioClient = audioClient;

            damageSubscriber.Subscribe(HandleDamage).AddTo(destroyCancellationToken);
        }

        public override void Render()
        {
            base.Render();
            if (IsAlive)
            {
                PlayRandomVoice();
            }

            if (Runner.IsSharedModeMasterClient && Time.time >= nextPlayerCheckTime)
            {
                if (!HasStateAuthority)
                {
                    nextPlayerCheckTime = Time.time + 2f;
                    if (!playersModel.TryGetPlayer(monster.StateAuthority, out _))
                    {
                        Debug.LogWarning("[MonsterBrainController]: Object lost State Authority! Requesting transfer...");
                        TransferAuthority().Forget();
                    }
                }

                ScanTarget();
            }

            if (HasStateAuthority)
            {
                UpdateStates();
                TryPlaceOnNavMesh();
            }
        }

        public override void Spawned()
        {
            base.Spawned();

            if (HasStateAuthority)
            {
                if (levelModel.Level.monsters != null && monster.MonsterDataIndex < levelModel.Level.monsters.Count)
                {
                    navMeshAgent.enabled = false;
                    var monsterData = levelModel.Level.monsters[monster.MonsterDataIndex];
                    monster.transform.position = monsterData.spawn;
                    hasPatrolArea = monsterData.HasPatrolArea;
                    navMeshAgent.enabled = true;
                    if (hasPatrolArea)
                    {
                        patrolBoundsXMin = monsterData.patrolCenter.x - 0.5f * Math.Abs(monsterData.patrolArea.x);
                        patrolBoundsXMax = monsterData.patrolCenter.x + 0.5f * Math.Abs(monsterData.patrolArea.x);

                        patrolBoundsZMin = monsterData.patrolCenter.z - 0.5f * Math.Abs(monsterData.patrolArea.z);
                        patrolBoundsZMax = monsterData.patrolCenter.z + 0.5f * Math.Abs(monsterData.patrolArea.z);

                        patrolBoundsYMin = monsterData.patrolCenter.y - 0.5f * Math.Abs(monsterData.patrolArea.y);
                        patrolBoundsYMax = monsterData.patrolCenter.y + 0.5f * Math.Abs(monsterData.patrolArea.y);
                    }
                }
            }
        }

        private void UpdateStates()
        {
            if (!HasTarget)
            {
                State = MonsterState.Patrol;
                Patrol();
            }
            else
            {
                if (IsInRange())
                {
                    State = MonsterState.Attack;
                    AttachToPlayer();
                    Attack();
                }
                else
                {
                    Chase();
                }
            }

            HandleTransitions();
        }

        private void Chase()
        {
            State = MonsterState.Chase;
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = ChaseSpeed;
            if (Time.time >= nextPathUpdateTime)
            {
                navMeshAgent.SetDestination(TargetPlayer.transform.position);
                nextPathUpdateTime = Time.time + pathUpdateInterval;
            }
        }

        private bool IsTargetInPatrolArea(Transform target)
        {
            if (!hasPatrolArea)
            {
                return true;
            }

            if (target == null)
            {
                return false;
            }

            var targetPos = target.position;

            return targetPos.x >= patrolBoundsXMin &&
                   targetPos.x <= patrolBoundsXMax &&
                   targetPos.z >= patrolBoundsZMin &&
                   targetPos.z <= patrolBoundsZMax &&
                   targetPos.y >= patrolBoundsYMin &&
                   targetPos.y <= patrolBoundsYMax;
        }

        private void HandleTransitions()
        {
            // Transitions
            // If we go from Attack to Not Attack
            if (previousFrameState == MonsterState.Attack && State != MonsterState.Attack)
            {
                navMeshAgent.enabled = true;
                if (State != MonsterState.DamageReceive)
                {
                    nextAttackTime = 0;
                }
            }

            // If we go from Not Attack to Attack
            if (previousFrameState != MonsterState.Attack && State == MonsterState.Attack)
            {
                navMeshAgent.enabled = false;
                if (previousFrameState != MonsterState.DamageReceive)
                {
                    nextAttackTime = 0;
                    SetAttackAudioRpc();
                }
            }

            // If we go from Not Chase to Chase
            if (previousFrameState != MonsterState.Chase && State == MonsterState.Chase)
            {
                SetScreamRpc(true);
            }

            // If we go from Chase to not Chase
            if (previousFrameState == MonsterState.Chase && State != MonsterState.Chase)
            {
                SetScreamRpc(false);
            }
        }

        private bool IsInRange()
        {
            return Vector3.Distance(TargetPlayer.transform.position, monster.transform.position) < monstersConfig.AttackMinDistance;
        }

        private void AttachToPlayer()
        {
            var targetPosition = TargetPlayer.transform.position + TargetPlayer.transform.forward * 2f;
            targetPosition.y -= 1.5f;
            monster.transform.position = targetPosition;
            monster.transform.rotation = Quaternion.LookRotation(-TargetPlayer.transform.forward.OnlyXZ().normalized, Vector3.up);
        }

        private void ScanTarget()
        {
            if (playersModel.TryFindClosestPlayer(monster.transform.position, monstersConfig.ScanPlayerRadius, IsPlayerTargetable, out var player, 3))
            {
                if (TargetPlayer == null)
                {
                    SetNewTargetPlayer(player);
                    return;
                }

                if (TargetPlayer != player && Time.time >= nextTargetSwitchTime)
                {
                    nextTargetSwitchTime = Time.time + targetSwitchInterval;
                    SetNewTargetPlayer(player);
                }
            }
            else
            {
                if (TargetPlayer == null)
                {
                    return;
                }

                SetNewTargetPlayer(null);
            }
        }

        private void HandleTargetPlayerChanged()
        {
            if (playersModel.LocalPlayer.Value == TargetPlayer && !HasStateAuthority)
            {
                TransferAuthority().Forget();
            }
        }

        private async UniTaskVoid TransferAuthority()
        {
            if (isTransferringAuthority)
            {
                return;
            }

            isTransferringAuthority = true;
            await Object.RequestStateAuthorityAsync(DespawnCancellationToken);
            isTransferringAuthority = false;
        }

        private void SetNewTargetPlayer(PlayerActor player)
        {
            if (HasStateAuthority)
            {
                TargetPlayer = player;
            }
            else
            {
                SetNewTargetRpc(player);
            }
        }

        private bool IsPlayerTargetable(PlayerActor player)
        {
            if (levelModel.LevelConfig.IsInfectedWhenDead)
            {
                return player.IsAlive && !player.IsTagged.Value;
            }

            if (hasPatrolArea && !IsTargetInPatrolArea(player.transform))
            {
                return false;
            }

            return player.IsAlive;
        }

        private void Patrol()
        {
            if (!navMeshAgent.isOnNavMesh)
            {
                return;
            }

            navMeshAgent.speed = PatrolSpeed;
            if (navMeshAgent.pathStatus == NavMeshPathStatus.PathPartial || navMeshAgent.remainingDistance < 0.5f)
            {
                navMeshAgent.SetDestination(GetRandomNavMeshPoint());
            }
        }

        private void Attack()
        {
            if (nextAttackTime == 0)
            {
                nextAttackTime = Time.time + monstersConfig.InitialAttackDelay;
                return;
            }

            if (IsAttacking)
            {
                return;
            }

            TargetPlayer.SetDamageByMonsterRpc(AttackDamage);
            nextAttackTime = Time.time + monstersConfig.AttackInterval;
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetScreamRpc(bool scream)
        {
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                if (scream)
                {
                    monster.PlayAudio(data.ChaseSoundKey, true);
                }
                else
                {
                    audioClient.Stop(data.ChaseSoundKey);
                }
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void SetAttackAudioRpc()
        {
            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                monster.PlayAudio(data.AttackSoundKey, true);
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void SetNewTargetRpc(PlayerActor playerActor)
        {
            if (HasStateAuthority)
            {
                TargetPlayer = playerActor;
            }
        }

        private void ReceiveDamage()
        {
            if (IsDamageReceiving)
            {
                return;
            }

            State = MonsterState.DamageReceive;
            nextDamageReceiveTime = Time.time + 0.5f;
        }

        private void TryPlaceOnNavMesh()
        {
            if (State == MonsterState.Attack)
            {
                return;
            }

            if (!navMeshAgent.enabled)
            {
                return;
            }

            if (navMeshAgent.isOnNavMesh)
            {
                return;
            }

            if (NavMesh.SamplePosition(monster.transform.position, out var hit, 10f, NavMesh.AllAreas))
            {
                var newPosition = new Vector3(monster.transform.position.x, hit.position.y, monster.transform.position.z);
                monster.transform.position = newPosition;
            }
        }

        private Vector3 GetRandomNavMeshPoint()
        {
            Vector3 searchOrigin;
            float searchRadius;

            if (hasPatrolArea)
            {
                searchRadius = 1f;
                searchOrigin = new Vector3(
                    Random.Range(patrolBoundsXMin, patrolBoundsXMax),
                    transform.position.y,
                    Random.Range(patrolBoundsZMin, patrolBoundsZMax)
                );

                if (NavMesh.SamplePosition(searchOrigin, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }
            else
            {
                searchOrigin = transform.position;
                searchRadius = 10f;
                var randomDirection = Random.insideUnitSphere * 10f;
                randomDirection += searchOrigin;

                if (NavMesh.SamplePosition(randomDirection, out var hit, searchRadius, NavMesh.AllAreas))
                {
                    return hit.position;
                }
            }

            return monster.transform.position;
        }

        private void PlayRandomVoice()
        {
            if (Time.time < nextVoiceTime)
            {
                return;
            }

            if (monstersConfig.TryGetMonsterData(monster.AvatarId, out var data))
            {
                monster.PlayAudio(data.RandomSoundKey);
            }

            nextVoiceTime = Time.time + Random.Range(5f, 15f);
        }

        private void HandleDamage(Monsters_MonsterDamageArgs args)
        {
            if (!HasStateAuthority || args.monster != monster || !IsAlive)
            {
                return;
            }

            ReceiveDamage();
        }
    }
}