using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class ReceiveInitialVoxelsController : ControllerBase
    {
        private const float ReceivingInitialVoxelsTimeout = 10;

        private VoxelSpaceManager voxelSpaceManager;
        private PlayersModel playersModel;
        private float lastInitialVoxelTime;
        private CancellationTokenSource updateCancellationTokenSource;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool IsVoxelWorldLoaded => voxelSpaceManager.IsMapLoaded.Value;

        [Inject]
        private void Construct(PlayersModel playersModel, VoxelSpaceManager voxelSpaceManager)
        {
            this.voxelSpaceManager = voxelSpaceManager;
            this.playersModel = playersModel;

            playersModel.LocalPlayer.Subscribe(_ => StartReceiveInitialVoxels()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.IsMapLoaded.Subscribe(_ => StartReceiveInitialVoxels()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelCreating.Subscribe(_ => UpdateLastInitialVoxelTime()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelDestroying.Subscribe(_ => UpdateLastInitialVoxelTime()).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelsCompleting.Subscribe(_ => CompleteInitialVoxels()).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            updateCancellationTokenSource.CancelAndDispose();
        }

        private void StartReceiveInitialVoxels()
        {
            updateCancellationTokenSource.CancelAndDispose();

            if (LocalPlayer == null || !IsVoxelWorldLoaded)
            {
                return;
            }

            lastInitialVoxelTime = -ReceivingInitialVoxelsTimeout;
            updateCancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = CancellationTokenSource.CreateLinkedTokenSource(updateCancellationTokenSource.Token, LocalPlayer.destroyCancellationToken).Token;
            UniTaskAsyncEnumerable.EveryUpdate().Subscribe(_ => ReceivingInitialVoxels()).AddTo(cancellationToken);
        }

        private void ReceivingInitialVoxels()
        {
            if (Time.time - lastInitialVoxelTime < ReceivingInitialVoxelsTimeout)
            {
                return;
            }

            if (TryGetInitialVoxelsSender(out var sender))
            {
                GameLogger.Voxels.Debug("Try get initial voxels from sender {0}", sender.StateAuthority.PlayerId);
                voxelSpaceManager.ReceivingInitialVoxels(sender.StateAuthority);
                lastInitialVoxelTime = Time.time;
            }
            else
            {
                CompleteInitialVoxels();
            }
        }

        private void UpdateLastInitialVoxelTime()
        {
            lastInitialVoxelTime = Time.time;
        }

        private void CompleteInitialVoxels()
        {
            updateCancellationTokenSource.CancelAndDispose();

            if (LocalPlayer != null)
            {
                GameLogger.Voxels.Debug("Complete initial voxels");
                LocalPlayer.SetInitialVoxelsReceived(true);
            }
        }

        private bool TryGetInitialVoxelsSender(out PlayerActor player)
        {
            var players = new List<PlayerActor>();

            foreach (var currentPlayer in playersModel.Players)
            {
                if (currentPlayer.HasStateAuthority || !currentPlayer.IsInitialVoxelsReceived.Value)
                {
                    continue;
                }

                players.Add(currentPlayer);
            }

            player = players.Count > 0 ? players.RandomItem() : null;
            return player != null;
        }
    }
}