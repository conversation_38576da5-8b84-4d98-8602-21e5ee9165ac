using Cysharp.Threading.Tasks;
using Fusion;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<int> dayCount = new AsyncReactiveProperty<int>(0);
        private readonly IAsyncReactiveProperty<int> killedZombieCount = new AsyncReactiveProperty<int>(0);

        public IReadOnlyAsyncReactiveProperty<int> DayCount => dayCount;
        public IReadOnlyAsyncReactiveProperty<int> KilledZombieCount => killedZombieCount;

        [Networked] [OnChangedRender(nameof(ChangeDayCountNetworked))]
        private int DayCountNetworked { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeKilledZombieCountNetworked))]
        private int KilledZombieCountNetworked { get; set; }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void ResetDayCountRpc()
        {
            SetDayCount(0);
        }

        public void SetActiveKilledZombieCountView(bool isActive)
        {
            playerView.SetActiveKilledZombieCountObject(isActive);
        }

        public void SetDayCount(int dayCountValue)
        {
            DayCountNetworked = dayCountValue;
        }

        public void IncrementDayCount()
        {
            SetDayCount(DayCountNetworked + 1);
        }

        public void SetKilledZombieCount(int killedZombieCountValue)
        {
            KilledZombieCountNetworked = killedZombieCountValue;
        }

        public void IncrementKilledZombieCount()
        {
            SetKilledZombieCount(KilledZombieCountNetworked + 1);
        }

        private void ChangeDayCountNetworked()
        {
            if (dayCount.Value != DayCountNetworked)
            {
                dayCount.Value = DayCountNetworked;
            }
        }

        private void ChangeKilledZombieCountNetworked()
        {
            if (killedZombieCount.Value != KilledZombieCountNetworked)
            {
                killedZombieCount.Value = KilledZombieCountNetworked;
            }

            if (HasStateAuthority)
            {
                playerView.SetKilledZombieCount(KilledZombieCountNetworked);
            }
        }

        public void SetActiveRaceObject(bool isActive)
        {
            playerView.SetActiveRaceObject(isActive);
        }

        public void SetRaceTimerText(string raceTimer)
        {
            playerView.SetRaceTimerText(raceTimer);
        }

        public void SetCheckpointNumber(string checkpointNumber)
        {
            playerView.SetCheckpointNumberText(checkpointNumber);
        }
    }
}