using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Weapons
{
    public abstract class WeaponActor : InteractableActor
    {
        [Header("Weapon")]
        [SerializeField] protected int damage = 5;
        [SerializeField] private bool canDamageVoxelWhenDrop;
        [SerializeField] private bool canDamagePlayerWhenDrop = true;
        [SerializeField] protected Transform viewParent;
        [SerializeField] private string hitAudioKey;
        [SerializeField] private string impactAudioKey;

        private IPublisher<WeaponDamageArgs> damagePublisher;
        private PlayersModel playersModel;

        public bool CanDamageVoxelWhenDrop => canDamageVoxelWhenDrop;
        public bool CanDamagePlayerWhenDrop => canDamagePlayerWhenDrop;
        public bool IsFirstCollisionAfterDropHappened { get; protected set; }

        protected WeaponsManager WeaponsManager { get; private set; }
        protected WeaponsConfig WeaponsConfig { get; private set; }
        protected IAudioClient AudioClient { get; private set; }
        protected IXRInput XRInput { get; private set; }

        [Networked] [OnChangedRender(nameof(ChangeIsTriggerButtonClicked))]
        protected NetworkBool IsTriggerButtonClicked { get; set; }
        private bool CanCollide => HasStateAuthority && (IsGrabbed || (!IsGrabbed && Rigidbody.velocity.sqrMagnitude > 0.01f));

        [Inject]
        private void Construct(
            WeaponsManager weaponsManager,
            WeaponsConfig weaponsConfig,
            PlayersModel playersModel,
            IAudioClient audioClient,
            IXRInput xrInput,
            IPublisher<WeaponDamageArgs> damagePublisher)
        {
            this.playersModel = playersModel;
            this.damagePublisher = damagePublisher;
            WeaponsManager = weaponsManager;
            WeaponsConfig = weaponsConfig;
            AudioClient = audioClient;
            XRInput = xrInput;
        }

        public override void Spawned()
        {
            base.Spawned();
            InitializeView();
            ChangeGrabber();
            ChangeIsAttached();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            StopDropTimer();
            UninitializeView();
        }

        protected override void HandleDropTimerExpired()
        {
            base.HandleDropTimerExpired();
            WeaponsManager.DestroyActor(this);
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);

            if (HasStateAuthority)
            {
                IsTriggerButtonClicked = false;
            }

            var onTrigger = args.interactorObject.IsLeftHand() ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onTrigger.Where(_ => HasStateAuthority).Subscribe(HandleTriggerButton).AddTo(DropCancellationToken);
        }

        protected override void HandleSelectExited(SelectExitEventArgs args)
        {
            base.HandleSelectExited(args);

            if (HasStateAuthority)
            {
                IsTriggerButtonClicked = false;
            }

            IsFirstCollisionAfterDropHappened = false;
        }

        protected virtual void ChangeIsTriggerButtonClicked()
        {
        }

        public virtual int GetDamage()
        {
            return damage;
        }

        protected virtual void DetectCollision(Collision collision)
        {
            damagePublisher.Publish(new WeaponDamageArgs(this, collision.GetContact(0)));

            PlayImpactAudio();

            if (!IsGrabbed)
            {
                IsFirstCollisionAfterDropHappened = true;
            }
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (!CanCollide)
            {
                return;
            }

            DetectCollision(collision);
        }

        private void HandleTriggerButton(InputAction.CallbackContext obj)
        {
            if (!HasStateAuthority)
            {
                return;
            }

            if (obj.performed)
            {
                IsTriggerButtonClicked = true;
            }
            else if (obj.canceled)
            {
                IsTriggerButtonClicked = false;
            }
        }

        private void PlayImpactAudio()
        {
            if (!HasStateAuthority || IsFirstCollisionAfterDropHappened || IsGrabbed)
            {
                return;
            }

            var playerList = playersModel.FindPlayers(transform.position);
            foreach (var player in playerList)
            {
                SendRpcSafe(() => PlayImpactAudioRpc(player.StateAuthority));
            }
        }

        public void PlayHitAudio()
        {
            if (!HasStateAuthority || !IsGrabbed)
            {
                return;
            }

            var playerList = playersModel.FindPlayers(transform.position);
            foreach (var player in playerList)
            {
                SendRpcSafe(() => PlayHitAudioRpc(player.StateAuthority));
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void PlayImpactAudioRpc([RpcTarget] PlayerRef player)
        {
            var key = string.IsNullOrEmpty(impactAudioKey) ? AudioKeys.ImpactDefault : impactAudioKey;
            AudioClient.Play(key, transform.position, destroyCancellationToken);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void PlayHitAudioRpc([RpcTarget] PlayerRef player)
        {
            var key = string.IsNullOrEmpty(hitAudioKey) ? AudioKeys.HitDefault : hitAudioKey;
            AudioClient.Play(key, transform.position, destroyCancellationToken);
        }
    }

    public abstract class WeaponActor<TView> : WeaponActor where TView : WeaponView
    {
        protected TView View { get; private set; }
        protected bool HasView => View != null;

        protected override void ChangeGrabber()
        {
            base.ChangeGrabber();

            if (IsGrabbed)
            {
                if (HasView)
                {
                    View.SetGrabState(HasStateAuthority);
                }
            }
            else
            {
                if (HasView)
                {
                    View.SetDropState(true);
                }
            }

            if (HasStateAuthority)
            {
                UpdateDropTimer(!IsGrabbed, WeaponsConfig.DropTimeout);
            }
        }

        protected override void InitializeView()
        {
            base.InitializeView();

            var view = WeaponsManager.CreateView(InteractableId, viewParent);

            if (view is TView currentView)
            {
                View = currentView;
            }
            else
            {
                WeaponsManager.DestroyView(view);
            }

            InteractableCode = WeaponsConfig.GetInteractableData(InteractableId)?.Code;
            InitializeGrabbing(View);
        }

        protected override void UninitializeView()
        {
            base.UninitializeView();
            UninitializeGrabbing();
            WeaponsManager?.DestroyView(View);
            View = null;
        }

        protected virtual void RotateView()
        {
            if (!HasView)
            {
                return;
            }

            View.transform.localRotation = IsGrabbed && IsTriggerButtonClicked ? Quaternion.Euler(180, 180, 0) : Quaternion.identity;
        }
    }
}