using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Services.Data;
using Modules.Core;
using Modules.Oculus;
using Modules.UnityGameServices;

namespace Game.Services
{
    internal partial class EconomyService : IEconomyService
    {
        private readonly IOculusClient oculusClient;
        private readonly IUnityGameServices unityGameServices;
        private readonly IServicesMockConfig servicesMockConfig;
        private readonly IAsyncReactiveProperty<int> coinAmount = new AsyncReactiveProperty<int>(0);
        private readonly IAsyncReactiveProperty<int> diamondAmount = new AsyncReactiveProperty<int>(0);

        public IReadOnlyAsyncReactiveProperty<int> CoinAmount => coinAmount;
        public IReadOnlyAsyncReactiveProperty<int> DiamondAmount => diamondAmount;

        public EconomyService(IOculusClient oculusClient, IUnityGameServices unityGameServices, IServicesMockConfig servicesMockConfig)
        {
            this.oculusClient = oculusClient;
            this.unityGameServices = unityGameServices;
            this.servicesMockConfig = servicesMockConfig;
        }

        public async UniTask<Result<PurchaseResult>> Purchase(ShopItemData shopItem, CancellationToken cancellationToken)
        {
            if (shopItem.HasOculusProduct)
            {
                var oculusPurchaseResult = await oculusClient.Purchase(shopItem.sku, shopItem.isConsumable, cancellationToken);

                if (oculusPurchaseResult.IsFail)
                {
                    return Result<PurchaseResult>.Fail(oculusPurchaseResult.Error);
                }
            }

            var unityPurchaseResult = await unityGameServices.Purchase(shopItem.purchaseId, cancellationToken);
            return unityPurchaseResult.IsOk ? Result<PurchaseResult>.Ok(GetPurchaseResult(unityPurchaseResult.Value)) : Result<PurchaseResult>.Fail(unityPurchaseResult.Error);
        }

        public async UniTask<Result<PurchaseResult>> PurchaseById(string purchaseId, CancellationToken cancellationToken)
        {
            var result = await unityGameServices.Purchase(purchaseId, cancellationToken);
            return result.IsOk ? Result<PurchaseResult>.Ok(GetPurchaseResult(result.Value)) : Result<PurchaseResult>.Fail(result.Error);
        }

        public async UniTask<Result> UpdateCoinAmount(CancellationToken cancellationToken)
        {
            var result = await unityGameServices.GetBalance(Constants.CoinId, cancellationToken);

            if (result.IsOk)
            {
                coinAmount.Value = result.Value;
            }

            return result;
        }

        public async UniTask<Result> AddCoinAmount(int coinAmount, CancellationToken cancellationToken)
        {
            var result = await unityGameServices.IncrementBalance(Constants.CoinId, coinAmount, cancellationToken);

            if (result.IsOk)
            {
                this.coinAmount.Value = result.Value;
            }

            return result;
        }

        public async UniTask<Result> UpdateDiamondAmount(CancellationToken cancellationToken)
        {
            var result = await unityGameServices.GetBalance(Constants.DiamondId, cancellationToken);

            if (result.IsOk)
            {
                diamondAmount.Value = result.Value;
            }

            return result;
        }

        public async UniTask<Result> AddDiamondAmount(int diamondAmount, CancellationToken cancellationToken)
        {
            var result = await unityGameServices.IncrementBalance(Constants.DiamondId, diamondAmount, cancellationToken);

            if (result.IsOk)
            {
                this.diamondAmount.Value = result.Value;
            }

            return result;
        }

        private PurchaseResult GetPurchaseResult(List<RewardDefinition> rewards)
        {
            var itemList = new List<PurchaseResult.RewardItem>();

            foreach (var reward in rewards)
            {
                itemList.Add(new PurchaseResult.RewardItem(reward.id, reward.amount));
            }

            return new PurchaseResult(itemList);
        }
    }
}