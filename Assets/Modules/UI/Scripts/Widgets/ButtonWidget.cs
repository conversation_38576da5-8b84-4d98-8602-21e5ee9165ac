using System;
using System.Reactive;
using System.Reactive.Subjects;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Modules.UI
{
    public abstract class ButtonWidget<T> : WidgetBase<T>
    {
        [SerializeField] protected Button mainButton;

        [SerializeField] protected TMP_Text mainText;

        private readonly ISubject<T> onClicked = new Subject<T>();

        public string TitleText => mainText == null ? string.Empty : mainText.text;
        public IObservable<T> OnClicked => onClicked;

        protected virtual void OnEnable()
        {
            mainButton.onClick.AddListener(HandleClick);
        }

        protected virtual void OnDisable()
        {
            mainButton.onClick.RemoveListener(HandleClick);
        }

        public virtual void SetMessage(string message)
        {
            if (mainText == null)
            {
                return;
            }

            mainText.text = message;
        }

        public virtual void SetInteractable(bool isInteractable)
        {
            mainButton.interactable = isInteractable;
        }

        protected virtual void HandleClick()
        {
            PlayClickAudio();
            onClicked.OnNext(WidgetData);
        }
    }

    public class ButtonWidget : ButtonWidget<Unit>
    {
    }
}