using System;
using System.Collections.Generic;
using Modules.Core;
using UnityEngine;

namespace Modules.XR
{
    public class HapticImpulseConfig : Config
    {
        [SerializeField] private List<Data> dataList;

        public float GetAmplitude(HapticImpulse hapticImpulse)
        {
            return dataList.Find(x => x.hapticImpulse == hapticImpulse)?.amplitude ?? 0;
        }

        public float GetDuration(HapticImpulse hapticImpulse)
        {
            return dataList.Find(x => x.hapticImpulse == hapticImpulse)?.duration ?? 0;
        }

        [Serializable]
        private class Data
        {
            public HapticImpulse hapticImpulse;
            public float amplitude;
            public float duration;
        }
    }
}