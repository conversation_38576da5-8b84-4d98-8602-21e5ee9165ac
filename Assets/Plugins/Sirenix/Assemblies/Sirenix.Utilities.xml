<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sirenix.Utilities</name>
    </assembly>
    <members>
        <member name="T:Sirenix.Utilities.ColorExtensions">
            <summary>
            Extension methods for the UnityEngine.Color type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.Lerp(UnityEngine.Color[],System.Single)">
            <summary>
            Lerps between multiple colors.
            </summary>
            <param name="colors">The colors.</param>
            <param name="t">The t.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.MoveTowards(UnityEngine.Color,UnityEngine.Color,System.Single)">
            <summary>
            Moves the towards implementation for Color.
            </summary>
            <param name="from">From color.</param>
            <param name="to">To color.</param>
            <param name="maxDelta">The maximum delta.</param>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.TryParseString(System.String,UnityEngine.Color@)">
            <summary>
            Tries to parse a string to a Color. The following formats are supported:
            "new Color(0.4, 0, 0, 1)", "#FFEEBBFF", "#FFEECC", "FFEEBBFF", "FFEECC"
            </summary>
            <param name="colorStr">The color string.</param>
            <param name="color">The color.</param>
            <returns>Returns true if the parse was a success.</returns>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.ToCSharpColor(UnityEngine.Color)">
            <summary>
            Converts a color to a string formatted to c#
            </summary>
            <param name="color">The color.</param>
            <returns>new Color(r, g, b, a)</returns>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.Pow(UnityEngine.Color,System.Single)">
            <summary>
            Pows the color with the specified factor.
            </summary>
            <param name="color">The color.</param>
            <param name="factor">The factor.</param>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.NormalizeRGB(UnityEngine.Color)">
            <summary>
            Normalizes the RGB values of the color ignoring the alpha value.
            </summary>
            <param name="color">The color.</param>
        </member>
        <member name="M:Sirenix.Utilities.ColorExtensions.PerceivedLuminosity(UnityEngine.Color,System.Boolean)">
            <summary>
            Gets the perceived luminosity of a given <see cref="T:UnityEngine.Color"/>.
            </summary>
            <param name="color">The current <see cref="T:UnityEngine.Color"/>.</param>
            <param name="includeAlpha">Determines if the <see cref="F:UnityEngine.Color.a">Color.a</see> value should impact the result.</param>
            <returns>The <see cref="T:System.Single">float</see> value representing the luminosity.</returns>
        </member>
        <member name="T:Sirenix.Utilities.DelegateExtensions">
            <summary>
            Delegate method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DelegateExtensions.Memoize``1(System.Func{``0})">
            <summary>
            Memoizes the specified func - returns the memoized version
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DelegateExtensions.Memoize``2(System.Func{``0,``1})">
            <summary>
            Memoizes the specified func - returns the memoized version
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.FieldInfoExtensions">
            <summary>
            FieldInfo method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.FieldInfoExtensions.IsAliasField(System.Reflection.FieldInfo)">
            <summary>
            Determines whether the specified field is an alias.
            </summary>
            <param name="fieldInfo">The field to check.</param>
            <returns>
              <c>true</c> if the specified field is an alias; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.FieldInfoExtensions.DeAliasField(System.Reflection.FieldInfo,System.Boolean)">
            <summary>
            Returns the original, backing field of an alias field if the field is an alias.
            </summary>
            <param name="fieldInfo">The field to check.</param>
            /// <param name="throwOnNotAliased">if set to <c>true</c> an exception will be thrown if the field is not aliased.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">The field was not aliased; this only occurs if throwOnNotAliased is true.</exception>
        </member>
        <member name="T:Sirenix.Utilities.GarbageFreeIterators">
            <summary>
            Garbage free enumerator methods.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.GFIterator``1(System.Collections.Generic.List{``0})">
            <summary>
            Garbage free enumerator for lists.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.GFIterator``2(System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            Garbage free enumerator for dictionaries.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.GFValueIterator``2(System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            Garbage free enumator for dictionary values.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.GFIterator``1(System.Collections.Generic.HashSet{``0})">
            <summary>
            Garbage free enumerator for hashsets.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1">
            <summary>
            List iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1.#ctor(System.Collections.Generic.List{`0})">
            <summary>
            Creates a list iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1.Current">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1.MoveNext">
            <summary>
            Moves to the next value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.ListIterator`1.Dispose">
            <summary>
            Disposes the iterator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1">
            <summary>
            Hashset iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1.#ctor(System.Collections.Generic.HashSet{`0})">
            <summary>
            Creates a hashset iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1.Current">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1.MoveNext">
            <summary>
            Moves to the next value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.HashsetIterator`1.Dispose">
            <summary>
            Disposes the iterator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2">
            <summary>
            Dictionary iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
            <summary>
            Creates a dictionary iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2.Current">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2.MoveNext">
            <summary>
            Moves to the next value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryIterator`2.Dispose">
            <summary>
            Disposes the iterator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2">
            <summary>
            Dictionary value iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
            <summary>
            Creates a dictionary value iterator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2.Current">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2.MoveNext">
            <summary>
            Moves to the next value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GarbageFreeIterators.DictionaryValueIterator`2.Dispose">
            <summary>
            Disposes the iterator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.LinqExtensions">
            <summary>
            Various LinQ extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.Examine``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Calls an action on each item before yielding them.
            </summary>
            <param name="source">The collection.</param>
            <param name="action">The action to call for each item.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Perform an action on each item.
            </summary>
            <param name="source">The source.</param>
            <param name="action">The action to perform.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Int32})">
            <summary>
            Perform an action on each item.
            </summary>
            <param name="source">The source.</param>
            <param name="action">The action to perform.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.Convert``1(System.Collections.IEnumerable,System.Func{System.Object,``0})">
            <summary>
            Convert each item in the collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="converter">Func to convert the items.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.ToImmutableList``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Convert a collection to an immutable list.
            </summary>
            <param name="source">The collection.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependWith``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0})">
            <summary>
            Add an item to the beginning of a collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="prepend">Func to create the item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependWith``1(System.Collections.Generic.IEnumerable{``0},``0)">
            <summary>
            Add an item to the beginning of a collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="prepend">The item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependWith``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the beginning of another collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="prepend">The collection to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,System.Func{``0})">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">Func to create the item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,``0)">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The collection to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},System.Func{``0})">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">Func to create the item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},``0)">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The collection to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Collections.Generic.IEnumerable{``0},System.Boolean},System.Func{``0})">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">Func to create the item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Collections.Generic.IEnumerable{``0},System.Boolean},``0)">
            <summary>
            Add an item to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The item to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.PrependIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Collections.Generic.IEnumerable{``0},System.Boolean},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the beginning of another collection, if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="prepend">The collection to prepend.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendWith``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0})">
            <summary>
            Add an item to the end of a collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="append">Func to create the item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendWith``1(System.Collections.Generic.IEnumerable{``0},``0)">
            <summary>
            Add an item to the end of a collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="append">The item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendWith``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the end of another collection.
            </summary>
            <param name="source">The collection.</param>
            <param name="append">The collection to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,System.Func{``0})">
            <summary>
            Add an item to the end of a collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">Func to create the item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,``0)">
            <summary>
            Add an item to the end of a collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">The item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Boolean,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the end of another collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">The collection to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},System.Func{``0})">
            <summary>
            Add an item to the end of a collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">Func to create the item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},``0)">
            <summary>
            Add an item to the end of a collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">The item to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AppendIf``1(System.Collections.Generic.IEnumerable{``0},System.Func{System.Boolean},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a collection to the end of another collection if a condition is met.
            </summary>
            <param name="source">The collection.</param>
            <param name="condition">The condition.</param>
            <param name="append">The collection to append.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.FilterCast``1(System.Collections.IEnumerable)">
            <summary>
            Returns and casts only the items of type <typeparamref name="T"/>.
            </summary>
            <param name="source">The collection.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AddRange``1(System.Collections.Generic.HashSet{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Adds a collection to a hashset.
            </summary>
            <param name="hashSet">The hashset.</param>
            <param name="range">The collection.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.IsNullOrEmpty``1(System.Collections.Generic.IList{``0})">
            <summary>
            Returns <c>true</c> if the list is either null or empty. Otherwise <c>false</c>.
            </summary>
            <param name="list">The list.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.Populate``1(System.Collections.Generic.IList{``0},``0)">
            <summary>
            Sets all items in the list to the given value.
            </summary>
            <param name="list">The list.</param>
            <param name="item">The value.</param>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.AddRange``1(System.Collections.Generic.IList{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Adds the elements of the specified collection to the end of the IList&lt;T&gt;.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.Sort``1(System.Collections.Generic.IList{``0},System.Comparison{``0})">
            <summary>
            Sorts an IList
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.LinqExtensions.Sort``1(System.Collections.Generic.IList{``0})">
            <summary>
            Sorts an IList
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ListExtensions">
            <summary>
            Various list extension methods.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ListExtensions.SetLength``1(System.Collections.Generic.IList{``0}@,System.Int32)">
            <summary>
            Increases or decrease the number of items in the list to the specified count.
            </summary>
            <param name="list">The list.</param>
            <param name="length">The new length.</param>
        </member>
        <member name="M:Sirenix.Utilities.ListExtensions.SetLength``1(System.Collections.Generic.IList{``0}@,System.Int32,System.Func{``0})">
            <summary>
            Increases or decrease the number of items in the list to the specified count.
            </summary>
            <param name="list">The list.</param>
            <param name="length">The new length.</param>
            <param name="newElement">Value of new elements.</param>
        </member>
        <member name="M:Sirenix.Utilities.ListExtensions.SetLength``1(System.Collections.Generic.IList{``0},System.Int32)">
            <summary>
            Increases or decrease the number of items in the list to the specified count.
            </summary>
            <param name="list">The list.</param>
            <param name="length">The new length.</param>
        </member>
        <member name="M:Sirenix.Utilities.ListExtensions.SetLength``1(System.Collections.Generic.IList{``0},System.Int32,System.Func{``0})">
            <summary>
            Increases or decrease the number of items in the list to the specified count.
            </summary>
            <param name="list">The list.</param>
            <param name="length">The new length.</param>
            <param name="newElement">Value of new elements.</param>
        </member>
        <member name="T:Sirenix.Utilities.MemberInfoExtensions">
            <summary>
            MemberInfo method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.IsDefined``1(System.Reflection.ICustomAttributeProvider,System.Boolean)">
            <summary>
            Returns true if the attribute whose type is specified by the generic argument is defined on this member
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.IsDefined``1(System.Reflection.ICustomAttributeProvider)">
            <summary>
            Returns true if the attribute whose type is specified by the generic argument is defined on this member
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttribute``1(System.Reflection.ICustomAttributeProvider,System.Boolean)">
            <summary>
            Returns the first found custom attribute of type T on this member
            Returns null if none was found
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttribute``1(System.Reflection.ICustomAttributeProvider)">
            <summary>
            Returns the first found non-inherited custom attribute of type T on this member
            Returns null if none was found
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttributes``1(System.Reflection.ICustomAttributeProvider)">
            <summary>
            Gets all attributes of the specified generic type.
            </summary>
            <param name="member">The member.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttributes``1(System.Reflection.ICustomAttributeProvider,System.Boolean)">
            <summary>
            Gets all attributes of the specified generic type.
            </summary>
            <param name="member">The member.</param>
            <param name="inherit">If true, specifies to also search the ancestors of element for custom attributes.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttributes(System.Reflection.ICustomAttributeProvider)">
            <summary>
            Gets all attribute instances defined on a MemeberInfo.
            </summary>
            <param name="member">The member.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetAttributes(System.Reflection.ICustomAttributeProvider,System.Boolean)">
            <summary>
            Gets all attribute instances on a MemberInfo.
            </summary>
            <param name="member">The member.</param>
            <param name="inherit">If true, specifies to also search the ancestors of element for custom attributes.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.GetNiceName(System.Reflection.MemberInfo)">
            <summary>
            If this member is a method, returns the full method name (name + params) otherwise the member name paskal splitted
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.IsStatic(System.Reflection.MemberInfo)">
            <summary>
            Determines whether a FieldInfo, PropertyInfo or MethodInfo is static.
            </summary>
            <param name="member">The member.</param>
            <returns>
              <c>true</c> if the specified member is static; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.IsAlias(System.Reflection.MemberInfo)">
            <summary>
            Determines whether the specified member is an alias.
            </summary>
            <param name="memberInfo">The member to check.</param>
            <returns>
              <c>true</c> if the specified member is an alias; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberInfoExtensions.DeAlias(System.Reflection.MemberInfo,System.Boolean)">
            <summary>
            Returns the original, backing member of an alias member if the member is an alias.
            </summary>
            <param name="memberInfo">The member to check.</param>
            /// <param name="throwOnNotAliased">if set to <c>true</c> an exception will be thrown if the member is not aliased.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">The member was not aliased; this only occurs if throwOnNotAliased is true.</exception>
        </member>
        <member name="T:Sirenix.Utilities.MethodInfoExtensions">
            <summary>
            Various extensions for MethodInfo.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.GetFullName(System.Reflection.MethodBase,System.String)">
            <summary>
            Returns the specified method's full name "methodName(argType1 arg1, argType2 arg2, etc)"
            Uses the specified gauntlet to replaces type names, ex: "int" instead of "Int32"
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.GetParamsNames(System.Reflection.MethodBase)">
            <summary>
            Returns a string representing the passed method parameters names. Ex "int num, float damage, Transform target"
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.GetFullName(System.Reflection.MethodBase)">
            <summary>
            Returns the specified method's full name.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.IsExtensionMethod(System.Reflection.MethodBase)">
            <summary>
            Tests if a method is an extension method.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.IsAliasMethod(System.Reflection.MethodInfo)">
            <summary>
            Determines whether the specified method is an alias.
            </summary>
            <param name="methodInfo">The method to check.</param>
            <returns>
              <c>true</c> if the specified method is an alias; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MethodInfoExtensions.DeAliasMethod(System.Reflection.MethodInfo,System.Boolean)">
            <summary>
            Returns the original, backing method of an alias method if the method is an alias.
            </summary>
            <param name="methodInfo">The method to check.</param>
            /// <param name="throwOnNotAliased">if set to <c>true</c> an exception will be thrown if the method is not aliased.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">The method was not aliased; this only occurs if throwOnNotAliased is true.</exception>
        </member>
        <member name="T:Sirenix.Utilities.Operator">
            <summary>
            Determines the type of operator.
            </summary>
            <seealso cref="T:Sirenix.Utilities.TypeExtensions" />
        </member>
        <member name="F:Sirenix.Utilities.Operator.Equality">
            <summary>
            The == operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Inequality">
            <summary>
            The != operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Addition">
            <summary>
            The + operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Subtraction">
            <summary>
            The - operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Multiply">
            <summary>
            The * operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Division">
            <summary>
            The / operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LessThan">
            <summary>
            The &lt; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.GreaterThan">
            <summary>
            The &gt; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LessThanOrEqual">
            <summary>
            The &lt;= operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.GreaterThanOrEqual">
            <summary>
            The &gt;= operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.Modulus">
            <summary>
            The % operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.RightShift">
            <summary>
            The &gt;&gt; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LeftShift">
            <summary>
            The &lt;&lt; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.BitwiseAnd">
            <summary>
            The &amp; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.BitwiseOr">
            <summary>
            The | operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.ExclusiveOr">
            <summary>
            The ^ operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.BitwiseComplement">
            <summary>
            The ~ operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LogicalAnd">
            <summary>
            The &amp;&amp; operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LogicalOr">
            <summary>
            The || operator.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Operator.LogicalNot">
            <summary>
            The ! operator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.PathUtilities">
            <summary>
            DirectoryInfo method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.GetDirectoryName(System.String)">
            <summary>
            Gets the name of the directory. Always returns forward slash seperators as opposed to Path.GetDirectoryName().
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.HasSubDirectory(System.IO.DirectoryInfo,System.IO.DirectoryInfo)">
            <summary>
            Determines whether the directory has a given directory in its hierarchy of children.
            </summary>
            <param name="parentDir">The parent directory.</param>
            <param name="subDir">The sub directory.</param>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.FindParentDirectoryWithName(System.IO.DirectoryInfo,System.String)">
            <summary>
            Finds a parent directory with a given name, or null if no such parent directory exists.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.CanMakeRelative(System.String,System.String)">
            <summary>
            Returns a value indicating whether or not a path can be made relative to another.
            </summary>
            <param name="absoluteParentPath">The parent path.</param>
            <param name="absolutePath">The path to make relative to the parent path.</param>
            <returns>A value indicating if the path can be made relative to the parent path.</returns>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.MakeRelative(System.String,System.String)">
            <summary>
            Returns a path string to path that is relative to the parent path.
            </summary>
            <param name="absoluteParentPath">The parent path.</param>
            <param name="absolutePath">The path to make relative to the parent path.</param>
            <returns>A relative path from parent path to path.</returns>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.TryMakeRelative(System.String,System.String,System.String@)">
            <summary>
            Tries to make a path that is relative from parent path to path.
            </summary>
            <param name="absoluteParentPath">The parent path.</param>
            <param name="absolutePath">The path to make relative to the parent path.</param>
            <param name="relativePath">A relative path from parent path to path. <c>null</c> if no relative path could be made.</param>
            <returns>A value indicating if the method succeeded in making a relative path.</returns>
        </member>
        <member name="M:Sirenix.Utilities.PathUtilities.Combine(System.String,System.String)">
            <summary>
            Combines two paths, and replaces all backslases with forward slash.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.PropertyInfoExtensions">
            <summary>
            PropertyInfo method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.PropertyInfoExtensions.IsAutoProperty(System.Reflection.PropertyInfo,System.Boolean)">
            <summary>
            Determines whether a property is an auto property.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.PropertyInfoExtensions.IsAliasProperty(System.Reflection.PropertyInfo)">
            <summary>
            Determines whether the specified property is an alias.
            </summary>
            <param name="propertyInfo">The property to check.</param>
            <returns>
              <c>true</c> if the specified property is an alias; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.PropertyInfoExtensions.DeAliasProperty(System.Reflection.PropertyInfo,System.Boolean)">
            <summary>
            Returns the original, backing property of an alias property if the property is an alias.
            </summary>
            <param name="propertyInfo">The property to check.</param>
            /// <param name="throwOnNotAliased">if set to <c>true</c> an exception will be thrown if the property is not aliased.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">The property was not aliased; this only occurs if throwOnNotAliased is true.</exception>
        </member>
        <member name="T:Sirenix.Utilities.RectExtensions">
            <summary>
            Defines a collection of handy Rect transformation methods, that can chained together for complex behaviour.
            Note that only the TakeX method defined here actually change the original Rect; the rest instead return a new transformed Rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetWidth(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect with the specified width.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetHeight(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect with the specified height.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetSize(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect with the specified size.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetSize(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect with the specified size.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="widthAndHeight">The desired width and height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetSize(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Returns a Rect with the specified size.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="size">The desired size of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.HorizontalPadding(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount on the X-axis.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="padding">The desired padding.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.HorizontalPadding(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount on the X-axis.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="left">Desired padding on the left side.</param>
            <param name="right">Desired padding on the right side.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.VerticalPadding(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount on the Y-axis.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="padding">The desired padding.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.VerticalPadding(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount on the Y-axis.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="top">The desired padding on the top.</param>
            <param name="bottom">The desired padding on the bottom.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Padding(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="padding">The desired padding.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Padding(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="horizontal">The desired horizontal padding.</param>
            <param name="vertical">The desired vertical padding.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Padding(UnityEngine.Rect,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been inserted by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="left">The desired padding on the left.</param>
            <param name="right">The desired padding on the right.</param>
            <param name="top">The desired padding on the top.</param>
            <param name="bottom">The desired padding on the bottom.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignLeft(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified width, that has been aligned to the left of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenter(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified width, that has been aligned to horizontal center of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenter(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect, with the specified width and height in the center of the provided rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignRight(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified width, that has been aligned to the right of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignRight(UnityEngine.Rect,System.Single,System.Boolean)">
            <summary>
            Returns a Rect, with the specified width, that has been aligned to the right of the original Rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignTop(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified height, that has been aligned to the top of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignMiddle(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified height, that has been aligned to the vertical middle of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignBottom(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified height, that has been aligned to the bottom of the original Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenterX(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified width, that has been aligned horizontally to the center of the original rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenterY(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified height, that has been aligned vertically to the center of the original rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenterXY(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect, with the specified width and height, that has been aligned horizontally and vertically to the center of the original rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="size">The desired width and height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AlignCenterXY(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect, with the specified width and height, that has been aligned horizontally and vertically to the center of the original rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="width">The desired width of the new Rect.</param>
            <param name="height">The desired height of the new Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Expand(UnityEngine.Rect,System.Single)">
            <summary>
            Returns a Rect that has been expanded by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="expand">The desired expansion.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Expand(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been expanded by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="horizontal">The desired expansion on the X-axis.</param>
            <param name="vertical">The desired expansion on the Y-axis.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Expand(UnityEngine.Rect,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Returns a Rect that has been expanded by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="left">The desired expansion on the left.</param>
            <param name="right">The desired expansion on the right.</param>
            <param name="top">The desired expansion on the top.</param>
            <param name="bottom">The desired expansion on the bottom.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.Split(UnityEngine.Rect,System.Int32,System.Int32)">
            <summary>
            Splits a Rect horizontally into the specified number of sub-rects, and returns a sub-rect for the specified index.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="index">The index for the subrect. Includes 0, and excludes count.</param>
            <param name="count">The amount of subrects the Rect should be split into.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SplitVertical(UnityEngine.Rect,System.Int32,System.Int32)">
            <summary>
            Splits a Rect vertically into the specified number of sub-rects, and returns a sub-rect for the specified index.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="index">The index for the subrect. Includes 0, and excludes count.</param>
            <param name="count">The amount of subrects the Rect should be split into.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SplitGrid(UnityEngine.Rect,System.Single,System.Single,System.Int32)">
            <summary>
            Splits a Rect into a grid from left to right and then down.
            </summary>
            <param name="rect">The original rect.</param>
            <param name="width">The width of a grid cell.</param>
            <param name="height">The height of a grid cell.</param>
            <param name="index">The index of the grid cell.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SplitTableGrid(UnityEngine.Rect,System.Int32,System.Single,System.Int32)">
            <summary>
            Splits a Rect into a grid from left to right and then down.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetCenterX(UnityEngine.Rect,System.Single)">
            <summary>
            Moves a Rect to the specified center X position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The desired center x position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetCenterY(UnityEngine.Rect,System.Single)">
            <summary>
            Moves a Rect to the specified center Y position.
            </summary>
            <param name="rect">The desired original Rect.</param>
            <param name="y">The desired desired center y position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetCenter(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Moves a Rect to the specified center position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The desired center X position.</param>
            <param name="y">The desired center Y position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetCenter(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Moves a Rect to the specified center position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="center">The desired center position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetPosition(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Moves a Rect to the specified position.
            </summary>
            <param name="rect">The orignal Rect.</param>
            <param name="position">The desired position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.ResetPosition(UnityEngine.Rect)">
            <summary>
            Resets a Rect's position to zero.
            </summary>
            <param name="rect">The original Rect.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddPosition(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Moves a Rect's position by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="move">The change in position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddPosition(UnityEngine.Rect,System.Single,System.Single)">
            <summary>
            Moves a Rect's position by the specified amount.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetX(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's X position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The desired X position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddX(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's X position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubX(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts from a Rect's X position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="x">The value to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetY(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's Y position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="y">The desired Y position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddY(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's Y position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="y">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubY(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts a Rect's Y position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="y">The value to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetMin(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Sets the min position of a Rect.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="min">The desired min position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddMin(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Adds to a Rect's min position.
            </summary>
            <param name="rect">The original rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubMin(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Subtracts a Rect's min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The vlaue to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetMax(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Sets a Rect's max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="max">The desired max position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddMax(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Adds to a Rect's max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubMax(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Subtracts a Rect's max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetXMin(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's X min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="xMin">The desired min X position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddXMin(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's X min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubXMin(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts from a Rect's X min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetXMax(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's X max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="xMax">The desired X max position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddXMax(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's X max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubXMax(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts a Rect's X max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetYMin(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's Y min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="yMin">The desired Y min.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddYMin(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's Y min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubYMin(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts a Rect's Y min position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to subtract.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SetYMax(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's Y max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="yMax">The desired Y max position.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.AddYMax(UnityEngine.Rect,System.Single)">
            <summary>
            Adds to a Rect's Y max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.SubYMax(UnityEngine.Rect,System.Single)">
            <summary>
            Subtracts from a Rect's Y max position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="value">The value to subtract.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.MinWidth(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's width, if it is less than the specified value.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="minWidth">The desired min width.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.MaxWidth(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's width, if it is greater than the specified value.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="maxWidth">The desired max width.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.MinHeight(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's height, if it is less than the specified value.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="minHeight">The desired min height.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.MaxHeight(UnityEngine.Rect,System.Single)">
            <summary>
            Sets a Rect's height, if it is greater than the specified value.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="maxHeight">The desired max height.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.ExpandTo(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Expands a rect to contain a given position.
            </summary>
            <param name="rect">The original Rect.</param>
            <param name="pos">The position to expand the rect towards.</param>
        </member>
        <member name="M:Sirenix.Utilities.RectExtensions.IsPlaceholder(UnityEngine.Rect)">
            <summary>
            Determines if an <see cref="T:UnityEngine.Rect"/> is a placeholder; usually (0, 0, 1, 1) in Layout.
            </summary>
            <param name="rect">The original <see cref="T:UnityEngine.Rect"/>.</param>
            <returns><c>true</c> if the <see cref="T:UnityEngine.Rect"/> is equal to (0, 0, 0, 0) or (0, 0, 1, 1); otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Sirenix.Utilities.StringExtensions">
            <summary>
            String method extensions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.StringExtensions.ToTitleCase(System.String)">
            <summary>
            Eg MY_INT_VALUE => MyIntValue
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.StringExtensions.Contains(System.String,System.String,System.StringComparison)">
            <summary>
            Returns whether or not the specified string is contained with this string
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.StringExtensions.SplitPascalCase(System.String)">
            <summary>
            Ex: "thisIsCamelCase" -> "This Is Camel Case"
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.StringExtensions.IsNullOrWhitespace(System.String)">
            <summary>
            Returns true if this string is null, empty, or contains only whitespace.
            </summary>
            <param name="str">The string to check.</param>
            <returns><c>true</c> if this string is null, empty, or contains only whitespace; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.StringExtensions.CalculateLevenshteinDistance(System.String,System.String)">
            <summary>
            O(n*m) - Use with care.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.TypeExtensions">
            <summary>
            Type method extensions.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.TypeExtensions.TypeNameAlternatives">
            <summary>
            Type name alias lookup.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsValidIdentifier(System.String)">
            <summary>
            Checks whether a given string is a valid CSharp identifier name. This also checks full type names including namespaces.
            </summary>
            <param name="identifier">The identifier to check.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsCastableTo(System.Type,System.Type,System.Boolean)">
            <summary>
            Determines whether a type can be casted to another type.
            </summary>
            <param name="from">From.</param>
            <param name="to">To.</param>
            <param name="requireImplicitCast">if set to <c>true</c> an implicit or explicit operator must be defined on the given type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCastMethodDelegate(System.Type,System.Type,System.Boolean)">
            <summary>
            If a type can be casted to another type, this provides a function to manually convert the type.
            </summary>
            <param name="from">From.</param>
            <param name="to">To.</param>
            <param name="requireImplicitCast">if set to <c>true</c> an implicit or explicit operator must be defined on the given type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCastMethodDelegate``2(System.Boolean)">
            <summary>
            If a type can be casted to another type, this provides a function to manually convert the type.
            </summary>
            <param name="requireImplicitCast">if set to <c>true</c> an implicit or explicit operator must be defined on the given type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCastMethod(System.Type,System.Type,System.Boolean)">
            <summary>
            If a type can be casted to another type, this provides the method info of the method in charge of converting the type.
            </summary>
            <param name="from">From.</param>
            <param name="to">To.</param>
            <param name="requireImplicitCast">if set to <c>true</c> an implicit or explicit operator must be defined on the given type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetEqualityComparerDelegate``1">
             <summary>
             Gets an equality comparer delegate used to compare the equality of values of a given type. In order, this will be:
            
             1. The == operator, if one is defined on the type.
             2. A delegate that uses <see cref="T:System.IEquatable`1"/>, if the type implements that interface.
             3. .NET's own <see cref="P:System.Collections.Generic.EqualityComparer`1.Default"/>
             </summary>
             <remarks>
             <para>Note that in the special case of the type <see cref="T:UnityEngine.Quaternion"/>, a special equality comparer is returned that only checks whether all the Quaternion components are equal.</para>
             <para>This is because, by default, Quaternion's equality operator is broken when operating on invalid quaternions; "default(Quaternion) == default(Quaternion)" evaluates to false, and this causes a multitude of problems.</para>
             <para>Special delegates are also returned for float and double, that consider float.NaN to be equal to float.NaN, and double.NaN to be equal to double.NaN.</para>
             </remarks>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetAttribute``1(System.Type,System.Boolean)">
            <summary>
            Gets the first attribute of type T. Returns null in the no attribute of type T was found.
            </summary>
            <param name="type">The type.</param>
            <param name="inherit">If true, specifies to also search the ancestors of element for custom attributes.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.ImplementsOrInherits(System.Type,System.Type)">
            <summary>
            Determines whether a type implements or inherits from another type.
            </summary>
            <param name="type">The type.</param>
            <param name="to">To.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.ImplementsOpenGenericType(System.Type,System.Type)">
            <summary>
            Determines whether a type implements an open generic interface or class such as IList&lt;&gt; or List&lt;&gt;.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericType">Type of the open generic type.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.ImplementsOpenGenericInterface(System.Type,System.Type)">
            <summary>
            Determines whether a type implements an open generic interface such as IList&lt;&gt;.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericInterfaceType">Type of the open generic interface.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException">Type " + openGenericInterfaceType.Name + " is not a generic type definition and an interface.</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.ImplementsOpenGenericClass(System.Type,System.Type)">
            <summary>
            Determines whether a type implements an open generic class such as List&lt;&gt;.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericType">Type of the open generic interface.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetArgumentsOfInheritedOpenGenericType(System.Type,System.Type)">
            <summary>
            Gets the generic arguments of an inherited open generic class or interface.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericType">The open generic type to get the arguments of.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetArgumentsOfInheritedOpenGenericClass(System.Type,System.Type)">
            <summary>
            Gets the generic arguments of an inherited open generic class.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericType">Type of the open generic class.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetArgumentsOfInheritedOpenGenericInterface(System.Type,System.Type)">
            <summary>
            Gets the generic arguments of an inherited open generic interface.
            </summary>
            <param name="candidateType">Type of the candidate.</param>
            <param name="openGenericInterfaceType">Type of the open generic interface.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetOperatorMethod(System.Type,Sirenix.Utilities.Operator,System.Type,System.Type)">
            <summary>
            Gets the MethodInfo of a specific operator kind, with the given left and right operands. This overload is *far* faster than any of the other GetOperatorMethod implementations, and should be used whenever possible.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetOperatorMethod(System.Type,Sirenix.Utilities.Operator)">
            <summary>
            Gets the MethodInfo of a specific operator type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetOperatorMethods(System.Type,Sirenix.Utilities.Operator)">
            <summary>
            Gets the MethodInfo of a specific operator type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetAllMembers(System.Type,System.Reflection.BindingFlags)">
            <summary>
            Gets all members from a given type, including members from all base types if the <see cref="F:System.Reflection.BindingFlags.DeclaredOnly"/> flag isn't set.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetAllMembers(System.Type,System.String,System.Reflection.BindingFlags)">
            <summary>
            Gets all members from a given type, including members from all base types.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetAllMembers``1(System.Type,System.Reflection.BindingFlags)">
            <summary>
            Gets all members of a specific type from a type, including members from all base types, if the <see cref="F:System.Reflection.BindingFlags.DeclaredOnly"/> flag isn't set.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetGenericBaseType(System.Type,System.Type)">
            <summary>
            Gets the generic type definition of an open generic base type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetGenericBaseType(System.Type,System.Type,System.Int32@)">
            <summary>
            Gets the generic type definition of an open generic base type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetBaseTypes(System.Type,System.Boolean)">
            <summary>
            Returns a lazy enumerable of all the base types of this type including interfaces and classes
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetBaseClasses(System.Type,System.Boolean)">
            <summary>
            Returns a lazy enumerable of all the base classes of this type
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetNiceName(System.Type)">
            <summary>
            Returns a nicely formatted name of a type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetNiceFullName(System.Type)">
            <summary>
            Returns a nicely formatted full name of a type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCompilableNiceName(System.Type)">
            <summary>
            Gets the name of the compilable nice.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCompilableNiceFullName(System.Type)">
            <summary>
            Gets the full name of the compilable nice.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCustomAttribute``1(System.Type,System.Boolean)">
            <summary>
            Returns the first found custom attribute of type T on this type
            Returns null if none was found
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCustomAttribute``1(System.Type)">
            <summary>
            Returns the first found non-inherited custom attribute of type T on this type
            Returns null if none was found
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCustomAttributes``1(System.Type)">
            <summary>
            Gets all attributes of type T.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetCustomAttributes``1(System.Type,System.Boolean)">
            <summary>
            Gets all attributes of type T.
            </summary>
            <param name="type">The type</param>
            <param name="inherit">If true, specifies to also search the ancestors of element for custom attributes.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsDefined``1(System.Type)">
            <summary>
            Returns true if the attribute whose type is specified by the generic argument is defined on this type
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsDefined``1(System.Type,System.Boolean)">
            <summary>
            Returns true if the attribute whose type is specified by the generic argument is defined on this type
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.InheritsFrom``1(System.Type)">
            <summary>
            Determines whether a type inherits or implements another type. Also include support for open generic base types such as List&lt;&gt;.
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.InheritsFrom(System.Type,System.Type)">
            <summary>
            Determines whether a type inherits or implements another type. Also include support for open generic base types such as List&lt;&gt;.
            </summary>
            <param name="type"></param>
            <param name="baseType"></param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetInheritanceDistance(System.Type,System.Type)">
            <summary>
            Gets the number of base types between given type and baseType.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.HasParamaters(System.Reflection.MethodInfo,System.Collections.Generic.IList{System.Type},System.Boolean)">
            <summary>
            Determines whether a method has the specified parameter types.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetReturnType(System.Reflection.MemberInfo)">
            <summary>
            FieldInfo will return the fieldType, propertyInfo the PropertyType, MethodInfo the return type and EventInfo will return the EventHandlerType.
            </summary>
            <param name="memberInfo">The MemberInfo.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetMemberValue(System.Reflection.MemberInfo,System.Object)">
            <summary>
            Gets the value contained in a given <see cref="T:System.Reflection.MemberInfo"/>. Currently only <see cref="T:System.Reflection.FieldInfo"/> and <see cref="T:System.Reflection.PropertyInfo"/> is supported.
            </summary>
            <param name="member">The <see cref="T:System.Reflection.MemberInfo"/> to get the value of.</param>
            <param name="obj">The instance to get the value from.</param>
            <returns>The value contained in the given <see cref="T:System.Reflection.MemberInfo"/>.</returns>
            <exception cref="T:System.ArgumentException">Can't get the value of the given <see cref="T:System.Reflection.MemberInfo"/> type.</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.SetMemberValue(System.Reflection.MemberInfo,System.Object,System.Object)">
            <summary>
            Sets the value of a given MemberInfo. Currently only <see cref="T:System.Reflection.FieldInfo"/> and <see cref="T:System.Reflection.PropertyInfo"/> is supported.
            </summary>
            <param name="member">The <see cref="T:System.Reflection.MemberInfo"/> to set the value of.</param>
            <param name="obj">The object to set the value on.</param>
            <param name="value">The value to set.</param>
            <exception cref="T:System.ArgumentException">
            Property has no setter
            or
            Can't set the value of the given <see cref="T:System.Reflection.MemberInfo"/> type.
            </exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.TryInferGenericParameters(System.Type,System.Type[]@,System.Type[])">
            <summary>//
            Tries to infer a set of valid generic parameters for a generic type definition, given a subset of known parameters.
            </summary>
            <param name="genericTypeDefinition">The generic type definition to attempt to infer parameters for.</param>
            <param name="inferredParams">The inferred parameters, if inferral was successful.</param>
            <param name="knownParameters">The known parameters to infer from.</param>
            <returns>True if the parameters could be inferred, otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException">
            genericTypeDefinition is null
            or
            knownParameters is null
            </exception>
            <exception cref="T:System.ArgumentException">The genericTypeDefinition parameter must be a generic type definition.</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.AreGenericConstraintsSatisfiedBy(System.Type,System.Type[])">
            <summary>
            <para>Checks whether an array of types satisfy the constraints of a given generic type definition.</para>
            <para>If this method returns true, the given parameters can be safely used with <see cref="M:System.Type.MakeGenericType(System.Type[])"/> with the given generic type definition.</para>
            </summary>
            <param name="genericType">The generic type definition to check.</param>
            <param name="parameters">The parameters to check validity for.</param>
            <exception cref="T:System.ArgumentNullException">
            genericType is null
            or
            types is null
            </exception>
            <exception cref="T:System.ArgumentException">The genericType parameter must be a generic type definition.</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.AreGenericConstraintsSatisfiedBy(System.Reflection.MethodBase,System.Type[])">
            <summary>
            <para>Checks whether an array of types satisfy the constraints of a given generic method definition.</para>
            <para>If this method returns true, the given parameters can be safely used with <see cref="M:System.Reflection.MethodInfo.MakeGenericMethod(System.Type[])"/> with the given generic method definition.</para>
            </summary>
            <param name="genericMethod">The generic method definition to check.</param>
            <param name="parameters">The parameters to check validity for.</param>
            <exception cref="T:System.ArgumentNullException">
            genericType is null
            or
            types is null
            </exception>
            <exception cref="T:System.ArgumentException">The genericMethod parameter must be a generic method definition.</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GenericParameterIsFulfilledBy(System.Type,System.Type,System.Collections.Generic.Dictionary{System.Type,System.Type},System.Collections.Generic.HashSet{System.Type})">
            <summary>
            Before calling this method we must ALWAYS hold a lock on the GenericConstraintsSatisfaction_LOCK object, as that is an implicit assumption it works with.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetGenericConstraintsString(System.Type,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetGenericParameterConstraintsString(System.Type,System.Boolean)">
            <summary>
            Formats a string with the specified generic parameter constraints on any given type. Example output: <c>where T : class</c>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GenericArgumentsContainsTypes(System.Type,System.Type[])">
            <summary>
            Determines whether a generic type contains the specified generic argument constraints.
            </summary>
            <param name="type">The type.</param>
            <param name="types">The generic argument types.</param>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsFullyConstructedGenericType(System.Type)">
            <summary>
            Determines whether a type is a fully constructed generic type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsNullableType(System.Type)">
            <summary>
            Determines whether a type is nullable by ensuring the type is neither a PrimitiveType, ValueType or an Enum.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.GetEnumBitmask(System.Object,System.Type)">
            <summary>
            Gets the enum bitmask in a ulong.
            </summary>
            <exception cref="T:System.ArgumentException">enumType</exception>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsCSharpKeyword(System.String)">
            <summary>
            Gets a value indicating if the string is a reserved C# keyword.
            </summary>
            <param name="identifier">The identifier to check.</param>
            <returns><c>true</c> if the string is a C# keyword. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.HasDefaultConstructor(System.Type)">
            <summary>
            <para>Determines if a given <see cref="T:System.Type"/> has a default constructor.</para>
            <para>A type is considered to have a default constructor if: It is a string, if it is an array, if it is a value type or if it has a public parameterless constructor.</para>
            </summary>
            <param name="self">The <see cref="T:System.Type"/> to investigate.</param>
            <returns><c>true</c> if a default constructor has been found; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.InstantiateDefault(System.Type,System.Boolean)">
            <summary>
            Attempts to instantiate an object of a given <see cref="T:System.Type"/> with it's default constructor. If no default constructor is found then it attempts to find the most suitable alternative constructor and instantiate the <see cref="T:System.Type"/> with default parameters.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to instantiate.</param>
            <param name="preferUninitializedOverNonDefault">Determines if the use of <see cref="M:System.Runtime.Serialization.FormatterServices.GetUninitializedObject(System.Type)"/> is preferred over a non-default constructor call.</param>
            <returns>The instantiated object or <c>null</c> if no suitable alternative constructor is found.</returns>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.FindIdealConstructor(System.Type,System.Reflection.BindingFlags)">
            <summary>
            Weighs multiple constructors for a given type, and attempts to find the most ideal constructor. This will ignore any unmanaged constructors.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> to weigh the constructors of.</param>
            <param name="flags">The <see cref="T:System.Reflection.BindingFlags"/> to search for the constructors; <see cref="F:System.Reflection.BindingFlags.Default"/> means only find the public ones.</param>
            <returns>The most ideal <see cref="T:System.Reflection.ConstructorInfo"/> based on the scoring system.</returns>
            <remarks>The scoring system prefers value types over reference types (adjusted for default values),
            it provides bonuses for empty constructors or constructors solely consisting of default values.
            Lastly it considers the amount of parameters present in the constructor in the overall score. </remarks>
        </member>
        <member name="M:Sirenix.Utilities.TypeExtensions.IsUnmanagedCtor(System.Reflection.ConstructorInfo)">
            <summary>
            Checks if a given <see cref="T:System.Reflection.ConstructorInfo"/> is unmanaged, by checking if it contains any <see cref="P:System.Type.IsPointer"/>, <see cref="P:System.Type.IsByRef"/> or <see cref="P:System.Type.IsMarshalByRef"/> parameters.
            </summary>
            <param name="ctor">The <see cref="T:System.Reflection.ConstructorInfo"/> to validate.</param>
            <returns><c>true</c> if the <see cref="T:System.Reflection.ConstructorInfo"/> is unmanaged; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Sirenix.Utilities.UnityExtensions">
            <summary>
            Extends various Unity classes.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.UnityExtensions.SafeIsUnityNull(UnityEngine.Object)">
            <summary>
            Determines whether a Unity object is null or "fake null",
            without ever calling Unity's own equality operators.
            This method is useful for checking if a Unity object is
            null, destroyed or missing at times when it is not allowed
            to call Unity's own equality operators, for example when
            not running on the main thread.
            </summary>
            <param name="obj">The Unity object to check.</param>
            <returns>True if the object is null, missing or destroyed; otherwise false.</returns>
        </member>
        <member name="T:Sirenix.Utilities.ArrayUtilities">
            <summary>
            Contains utilities for operating on arrays.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ArrayUtilities.CreateNewArrayWithAddedElement``1(``0[],``0)">
            <summary>
            Creates a new array with an added element.
            </summary>
            <typeparam name="T">The element type of the array.</typeparam>
            <param name="array">The array.</param>
            <param name="value">The value to add.</param>
            <returns>The new array.</returns>
            <exception cref="T:System.ArgumentNullException">The given array was null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.ArrayUtilities.CreateNewArrayWithInsertedElement``1(``0[],System.Int32,``0)">
            <summary>
            Creates a new array with an element inserted at a given index.
            </summary>
            <typeparam name="T">The element type of the array.</typeparam>
            <param name="array">The array.</param>
            <param name="index">The index to insert at.</param>
            <param name="value">The value to insert.</param>
            <exception cref="T:System.ArgumentNullException">The given array was null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The index to insert at was out of range.</exception>
        </member>
        <member name="M:Sirenix.Utilities.ArrayUtilities.CreateNewArrayWithRemovedElement``1(``0[],System.Int32)">
            <summary>
            Creates a new array with an element removed.
            </summary>
            <typeparam name="T">The element type of the array.</typeparam>
            <param name="array">The array.</param>
            <param name="index">The index to remove an element at.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException">The given array was null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The given index to remove an element at was out of range.</exception>
        </member>
        <member name="T:Sirenix.Utilities.Editor.AssetScriptGuidUtility">
            <summary>
            Utility class for asset Guid script
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetScriptGuidUtility.TryUpdateAssetScriptGuid(System.String,System.Type)">
            <summary>
            Tries to update the Guid of a specified asset with the Guid from a specified script type.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.DeepReflection">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateWeakStaticValueGetter(System.Type,System.Type,System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateWeakInstanceValueGetter(System.Type,System.Type,System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateWeakInstanceValueSetter(System.Type,System.Type,System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateWeakInstanceValueGetter``1(System.Type,System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateValueGetter``1(System.Type,System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DeepReflection.CreateValueGetter``2(System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.DoubleLookupDictionary`3">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.DoubleLookupDictionary`3.Item(`0)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.InnerCount(`0)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.TotalInnerCount">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.ContainsKeys(`0,`1)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.TryGetInnerValue(`0,`1,`2@)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.AddInner(`0,`1,`2)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.RemoveInner(`0,`1)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.DoubleLookupDictionary`3.RemoveWhere(System.Func{`2,System.Boolean})">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.WeakValueGetter">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.WeakValueSetter">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.WeakValueGetter`1">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.WeakValueSetter`1">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ValueGetter`2">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ValueSetter`2">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.EmitUtilities">
            <summary>
            Provides utilities for using the <see cref="N:System.Reflection.Emit"/> namespace.
            <para />
            This class is due for refactoring. Use at your own peril.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.EmitUtilities.CanEmit">
            <summary>
            Gets a value indicating whether emitting is supported on the current platform.
            </summary>
            <value>
              <c>true</c> if the current platform can emit; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateStaticFieldGetter``1(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which gets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <typeparam name="FieldType">The type of the field to get a value from.</typeparam>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakStaticFieldGetter(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which gets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateStaticFieldSetter``1(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which sets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <typeparam name="FieldType">The type of the field to set a value to.</typeparam>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a setter for.</param>
            <returns>A delegate which sets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakStaticFieldSetter(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which sets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a setter for.</param>
            <returns>A delegate which sets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstanceFieldGetter``2(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which gets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <typeparam name="InstanceType">The type of the instance to get a value from.</typeparam>
            <typeparam name="FieldType">The type of the field to get a value from.</typeparam>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceFieldGetter``1(System.Type,System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which gets the value of a field from a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <typeparam name="FieldType">The type of the field to get a value from.</typeparam>
            <param name="instanceType">The <see cref="T:System.Type"/> of the instance to get a value from.</param>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceFieldGetter(System.Type,System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which gets the weakly typed value of a field from a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <param name="instanceType">The <see cref="T:System.Type"/> of the instance to get a value from.</param>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstanceFieldSetter``2(System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which sets the value of a field. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <typeparam name="InstanceType">The type of the instance to set a value on.</typeparam>
            <typeparam name="FieldType">The type of the field to set a value to.</typeparam>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a setter for.</param>
            <returns>A delegate which sets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceFieldSetter``1(System.Type,System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which sets the value of a field on a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <typeparam name="FieldType">The type of the field to set a value to.</typeparam>
            <param name="instanceType">Type of the instance.</param>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo" /> instance describing the field to create a setter for.</param>
            <returns>
            A delegate which sets the value of the given field.
            </returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
            <exception cref="T:System.ArgumentException">Field cannot be static.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceFieldSetter(System.Type,System.Reflection.FieldInfo)">
            <summary>
            Creates a delegate which sets the weakly typed value of a field on a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <param name="instanceType">Type of the instance.</param>
            <param name="fieldInfo">The <see cref="T:System.Reflection.FieldInfo" /> instance describing the field to create a setter for.</param>
            <returns>
            A delegate which sets the value of the given field.
            </returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
            <exception cref="T:System.ArgumentException">Field cannot be static.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstancePropertyGetter(System.Type,System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which gets the weakly typed value of a field from a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <param name="instanceType">The <see cref="T:System.Type"/> of the instance to get a value from.</param>
            <param name="propertyInfo">The <see cref="T:System.Reflection.FieldInfo"/> instance describing the field to create a getter for.</param>
            <returns>A delegate which gets the value of the given field.</returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstancePropertySetter(System.Type,System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which sets the weakly typed value of a property on a weakly typed instance of a given type. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <param name="instanceType">Type of the instance.</param>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo" /> instance describing the property to create a setter for.</param>
            <returns>
            A delegate which sets the value of the given field.
            </returns>
            <exception cref="T:System.ArgumentNullException">The fieldInfo parameter is null.</exception>
            <exception cref="T:System.ArgumentException">Property cannot be static.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateStaticPropertySetter``1(System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which sets the value of a property. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <typeparam name="PropType">The type of the property to set a value to.</typeparam>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> instance describing the property to create a setter for.</param>
            <returns>A delegate which sets the value of the given property.</returns>
            <exception cref="T:System.ArgumentNullException">The propertyInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateStaticPropertyGetter``1(System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which gets the value of a property. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <typeparam name="PropType">The type of the property to get a value from.</typeparam>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> instance describing the property to create a getter for.</param>
            <returns>A delegate which gets the value of the given property.</returns>
            <exception cref="T:System.ArgumentNullException">The propertyInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstancePropertySetter``2(System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which sets the value of a property. If emitting is not supported on the current platform, the delegate will use reflection to set the value.
            </summary>
            <typeparam name="InstanceType">The type of the instance to set a value on.</typeparam>
            <typeparam name="PropType">The type of the property to set a value to.</typeparam>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> instance describing the property to create a setter for.</param>
            <returns>A delegate which sets the value of the given property.</returns>
            <exception cref="T:System.ArgumentNullException">The propertyInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstancePropertyGetter``2(System.Reflection.PropertyInfo)">
            <summary>
            Creates a delegate which gets the value of a property. If emitting is not supported on the current platform, the delegate will use reflection to get the value.
            </summary>
            <typeparam name="InstanceType">The type of the instance to get a value from.</typeparam>
            <typeparam name="PropType">The type of the property to get a value from.</typeparam>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/> instance describing the property to create a getter for.</param>
            <returns>A delegate which gets the value of the given property.</returns>
            <exception cref="T:System.ArgumentNullException">The propertyInfo parameter is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateMethodReturner``2(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given parameterless instance method and returns the result.
            </summary>
            <typeparam name="InstanceType">The type of the class which the method is on.</typeparam>
            <typeparam name="ReturnType">The type which is returned by the given method info.</typeparam>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.</returns>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateStaticMethodCaller(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given parameterless static method.
            </summary>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.</returns>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceMethodCaller``1(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given parameterless weakly typed instance method.
            </summary>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.</returns>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceMethodCaller(System.Reflection.MethodInfo)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceMethodCaller``2(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given weakly typed instance method with one argument and returns a value.
            </summary>
            <typeparam name="TResult">The type of the result.</typeparam>
            <typeparam name="TArg1">The type of the first argument.</typeparam>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>
            A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.
            </returns>
            <exception cref="T:System.ArgumentNullException">methodInfo</exception>
            <exception cref="T:System.ArgumentException">
            Given method ' + methodInfo.Name + ' is static when it has to be an instance method.
            or
            Given method ' + methodInfo.Name + ' must return type  + typeof(TResult) + .
            or
            Given method ' + methodInfo.Name + ' must have exactly one parameter.
            or
            The first parameter of the method ' + methodInfo.Name + ' must be of type  + typeof(TArg1) + .
            </exception>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceMethodCallerFunc``1(System.Reflection.MethodInfo)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateWeakInstanceMethodCallerFunc``2(System.Reflection.MethodInfo)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstanceMethodCaller``1(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given parameterless instance method.
            </summary>
            <typeparam name="InstanceType">The type of the class which the method is on.</typeparam>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.</returns>
        </member>
        <member name="M:Sirenix.Utilities.EmitUtilities.CreateInstanceMethodCaller``2(System.Reflection.MethodInfo)">
            <summary>
            Creates a fast delegate method which calls a given instance method with a given argument.
            </summary>
            <typeparam name="InstanceType">The type of the class which the method is on.</typeparam>
            <typeparam name="Arg1">The type of the argument with which to call the method.</typeparam>
            <param name="methodInfo">The method info instance which is used.</param>
            <returns>A delegate which calls the method and returns the result, except it's hundreds of times faster than MethodInfo.Invoke.</returns>
        </member>
        <member name="T:Sirenix.Utilities.Flags">
            <summary>
            This class encapsulates common <see cref="T:System.Reflection.BindingFlags"/> combinations.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.AnyVisibility">
            <summary>
            Search criteria encompassing all public and non-public members, including base members.
            Note that you also need to specify either the Instance or Static flag.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstancePublic">
            <summary>
            Search criteria encompassing all public instance members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstancePrivate">
            <summary>
            Search criteria encompassing all non-public instance members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstanceAnyVisibility">
            <summary>
            Search criteria encompassing all public and non-public instance members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticPublic">
            <summary>
            Search criteria encompassing all public static members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticPrivate">
            <summary>
            Search criteria encompassing all non-public static members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticAnyVisibility">
            <summary>
            Search criteria encompassing all public and non-public static members, including base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstancePublicDeclaredOnly">
            <summary>
            Search criteria encompassing all public instance members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstancePrivateDeclaredOnly">
            <summary>
            Search criteria encompassing all non-public instance members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.InstanceAnyDeclaredOnly">
            <summary>
            Search criteria encompassing all public and non-public instance members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticPublicDeclaredOnly">
            <summary>
            Search criteria encompassing all public static members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticPrivateDeclaredOnly">
            <summary>
            Search criteria encompassing all non-public static members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticAnyDeclaredOnly">
            <summary>
            Search criteria encompassing all public and non-public static members, excluding base members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.StaticInstanceAnyVisibility">
            <summary>
            Search criteria encompassing all members, including base and static members.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Flags.AllMembers">
            <summary>
            Search criteria encompassing all members (public and non-public, instance and static), including base members.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GlobalConfig`1">
             <summary>
             <para>
             A GlobalConfig singleton, automatically created and saved as a ScriptableObject in the project at the specified path.
             This only happens if the UnityEditor is present. If it's not, a non-persistent ScriptableObject is created at run-time.
             </para>
             <para>
             Remember to locate the path within a resources folder if you want the config file to be loaded at runtime without the Unity editor being present.
             </para>
             <para>
             The asset path is specified by defining a <see cref="T:Sirenix.Utilities.GlobalConfigAttribute"/>. If no attribute is defined it will be saved in the root assets folder.
             </para>
             </summary>
             <example>
             <code>
             [GlobalConfig("Assets/Resources/MyConfigFiles/")]
             public class MyGlobalConfig : GlobalConfig&lt;MyGlobalConfig&gt;
             {
                 public int MyGlobalVariable;
             }
            
             void SomeMethod()
             {
                 int value = MyGlobalConfig.Instance.MyGlobalVariable;
             }
             </code>
             </example>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfig`1.HasInstanceLoaded">
            <summary>
            Gets a value indicating whether this instance has instance loaded.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfig`1.Instance">
            <summary>
            Gets the singleton instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GlobalConfig`1.LoadInstanceIfAssetExists">
            <summary>
            Tries to load the singleton instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GlobalConfig`1.OpenInEditor">
            <summary>
            Opens the config in a editor window. This is currently only used internally by the Sirenix.OdinInspector.Editor assembly.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigUtility`1.HasInstanceLoaded">
            <summary>
            Gets a value indicating whether this instance has instance loaded.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GlobalConfigUtility`1.GetInstance(System.String,System.String)">
            <summary>
            Gets the singleton instance.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GlobalConfigAttribute">
            <summary>
            <para>This attribute is used by classes deriving from GlobalConfig and specifies the asset path for the generated config file.</para>
            </summary>
            <seealso cref="!:EditorGlobalConfigAttribute"/>
            <seealso cref="T:Sirenix.Utilities.GlobalConfig`1"/>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigAttribute.FullPath">
            <summary>
            Gets the full asset path including Application.dataPath. Only relevant if IsInResourcesFolder is false.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigAttribute.AssetPath">
            <summary>
            Gets the relative asset path. Only relevant if IsInResourcesFolder is false.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigAttribute.ResourcesPath">
            <summary>
            Gets the resources path. Only relevant if IsInResourcesFolder is true.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigAttribute.UseAsset">
            <summary>
            Whether the config should be associated with an asset in the project. If false, no config asset will be generated or loaded, and a new "temporary" config instance will be created for every reload. This is true by default.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.GlobalConfigAttribute.IsInResourcesFolder">
            <summary>
            Gets a value indicating whether this asset is located within a resource folder.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GlobalConfigAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.GlobalConfigAttribute"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GlobalConfigAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.GlobalConfigAttribute"/> class.
            </summary>
            <param name="assetPath">The relative asset. Remember to locate the path within a resources folder if you want the config file to be loaded at runtime without the Unity Editor.</param>
        </member>
        <member name="T:Sirenix.Utilities.GUILayoutOptions">
             <summary>
             <para>
             GUILayoutOptions is a handy utility that provides cached GUILayoutOpion arrays based on the wanted parameters.
             </para>
             </summary>
             <example>
             <para>
             Most GUILayout and EditorGUILayout methods takes an optional "params GUILayoutOption[]" parameter.
             Each time you call this, an array is allocated generating garbage.
             </para>
             <code>
             // Generates garbage:
             GUILayout.Label(label, GUILayout.Label(label, GUILayout.Width(20), GUILayout.ExpandHeight(), GUILayout.MaxWidth(300)));
            
             // Does not generate garbage:
             GUILayout.Label(label, GUILayout.Label(label, GUILayoutOptions.Width(20).ExpandHeight().MaxWidth(300)));
             </code>
             </example>
        </member>
        <member name="F:Sirenix.Utilities.GUILayoutOptions.EmptyGUIOptions">
            <summary>
            An EmptyGUIOption[] array with a length of 0.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance">
            <summary>
            A GUILayoutOptions instance with an implicit operator to be converted to a GUILayoutOption[] array.
            </summary>
            <seealso cref="T:Sirenix.Utilities.GUILayoutOptions"/>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.op_Implicit(Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance)~UnityEngine.GUILayoutOption[]">
            <summary>
            Gets or creates the cached GUILayoutOption array based on the layout options specified.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.Width(System.Single)">
            <summary>
            Option passed to a control to give it an absolute width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.Height(System.Single)">
            <summary>
            Option passed to a control to give it an absolute height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.MaxHeight(System.Single)">
            <summary>
            Option passed to a control to specify a maximum height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.MaxWidth(System.Single)">
            <summary>
            Option passed to a control to specify a maximum width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.MinHeight(System.Single)">
            <summary>
            Option passed to a control to specify a minimum height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.MinWidth(System.Single)">
            <summary>
            Option passed to a control to specify a minimum width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.ExpandHeight(System.Boolean)">
            <summary>
            Option passed to a control to allow or disallow vertical expansion.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.ExpandWidth(System.Boolean)">
            <summary>
            Option passed to a control to allow or disallow horizontal expansion.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.Equals(Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance)">
            <summary>
            Determines whether the instance is equals another instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.GUILayoutOptionsInstance.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.Width(System.Single)">
            <summary>
            Option passed to a control to give it an absolute width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.Height(System.Single)">
            <summary>
            Option passed to a control to give it an absolute height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.MaxHeight(System.Single)">
            <summary>
            Option passed to a control to specify a maximum height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.MaxWidth(System.Single)">
            <summary>
            Option passed to a control to specify a maximum width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.MinWidth(System.Single)">
            <summary>
            Option passed to a control to specify a minimum width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.MinHeight(System.Single)">
            <summary>
            Option passed to a control to specify a minimum height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.ExpandHeight(System.Boolean)">
            <summary>
            Option passed to a control to allow or disallow vertical expansion.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.GUILayoutOptions.ExpandWidth(System.Boolean)">
            <summary>
            Option passed to a control to allow or disallow horizontal expansion.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ImmutableHashSet`1">
            <summary>
            Immutable hashset wraps another hashset, and allows for reading the inner hashset, without the ability to change it.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableHashSet`1.#ctor(System.Collections.Generic.HashSet{`0})">
            <summary>
            Creates an immutable hashset around another hashset.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableHashSet`1.Contains(`0)">
            <summary>
            Returns <c>true</c> if the item is contained in the list.
            </summary>
            <param name="item">The item's value.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableHashSet`1.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableHashSet`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.IImmutableList">
            <summary>
            Interface for immutable list.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.IImmutableList`1">
            <summary>
            Interface for generic immutable list.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.IImmutableList`1.Item(System.Int32)">
            <summary>
            Index accessor.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ImmutableList">
            <summary>
            Immutable list wraps another list, and allows for reading the inner list, without the ability to change it.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.#ctor(System.Collections.IList)">
            <summary>
            Creates an immutable list around another list.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.Count">
            <summary>
            Number of items in the list.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.IsFixedSize">
            <summary>
            Immutable list cannot be changed directly, so it's size is always fixed.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.IsReadOnly">
            <summary>
            Immutable list are always readonly.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.IsSynchronized">
            <summary>
            Returns <c>true</c> if the inner list is synchronized.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.SyncRoot">
            <summary>
            Gets the sync root object.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList.Item(System.Int32)">
            <summary>
            Index accessor.
            </summary>
            <param name="index">Index.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.Contains(System.Object)">
            <summary>
            Returns <c>true</c> if the item is contained in the list.
            </summary>
            <param name="value">The item's value.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.CopyTo(System.Object[],System.Int32)">
            <summary>
            Copy the list to an array,
            </summary>
            <param name="array">Target array.</param>
            <param name="arrayIndex">Index.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.CopyTo(System.Array,System.Int32)">
            <summary>
            Copy the list to an array,
            </summary>
            <param name="array">Target array.</param>
            <param name="index">Index.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.GetEnumerator">
            <summary>
            Gets an enumerator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.IndexOf(System.Object)">
            <summary>
            Get the index of a value.
            </summary>
            <param name="value">The item's value.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.System#Collections#Generic#IList{System#Object}#RemoveAt(System.Int32)">
            <summary>
            Immutable list cannot be edited.
            </summary>
            <param name="index">Index.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.System#Collections#Generic#IList{System#Object}#Insert(System.Int32,System.Object)">
            <summary>
            Immutable list cannot be edited.
            </summary>
            <param name="index">Index.</param>
            <param name="item">Item.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.System#Collections#Generic#ICollection{System#Object}#Add(System.Object)">
            <summary>
            Immutable list cannot be edited.
            </summary>
            <param name="item">Item.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.System#Collections#Generic#ICollection{System#Object}#Clear">
            <summary>
            Immutable list cannot be edited.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList.System#Collections#Generic#ICollection{System#Object}#Remove(System.Object)">
            <summary>
            Immutable list cannot be edited.
            </summary>
            <param name="item">Item.</param>
        </member>
        <member name="T:Sirenix.Utilities.ImmutableList`1">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`1.#ctor(System.Collections.Generic.IList{`0})">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`1.Count">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`1.IsReadOnly">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`1.Item(System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`1.Contains(`0)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`1.CopyTo(`0[],System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`1.GetEnumerator">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`1.IndexOf(`0)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ImmutableList`2">
            <summary>
            Immutable list wraps another list, and allows for reading the inner list, without the ability to change it.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`2.#ctor(`0)">
            <summary>
            Creates an immutable list around another list.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`2.Count">
            <summary>
            Number of items in the list.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`2.IsReadOnly">
            <summary>
            Immutable list are always readonly.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.ImmutableList`2.Item(System.Int32)">
            <summary>
            Index accessor.
            </summary>
            <param name="index">Index.</param>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`2.Contains(`1)">
            <summary>
            Returns <c>true</c> if the item is contained in the list.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`2.CopyTo(`1[],System.Int32)">
            <summary>
            Copies the list to an array.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`2.GetEnumerator">
            <summary>
            Gets an enumerator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ImmutableList`2.IndexOf(`1)">
            <summary>
            Gets the index of an item.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.MathUtilities">
            <summary>
            Collection of math function.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.PointDistanceToLine(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">
            <summary>
            Distance from a point to a line.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Hermite(System.Single,System.Single,System.Single)">
            <summary>
            Returns a smooth value between start and end based on t.
            </summary>
            <param name="start">First point.</param>
            <param name="end">Second point.</param>
            <param name="t">Position between 0 and 1.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.StackHermite(System.Single,System.Single,System.Single,System.Int32)">
            <summary>
            Returns a smooth value between start and end based on t.
            </summary>
            <param name="start">First point.</param>
            <param name="end">Second point.</param>
            <param name="t">Position between 0 and 1.</param>
            <param name="count">Number of interpolations to make.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Fract(System.Single)">
            <summary>
            Returns the fractional of the value.
            </summary>
            <param name="value">The value to get the fractional of.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Fract(UnityEngine.Vector2)">
            <summary>
            Returns the fractional of the value.
            </summary>
            <param name="value">The value to get the fractional of.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Fract(UnityEngine.Vector3)">
            <summary>
            Returns the fractional of the value.
            </summary>
            <param name="value">The value to get the fractional of.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.BounceEaseInFastOut(System.Single)">
            <summary>
            Returns a value based on t, that bounces faster and faster.
            </summary>
            <param name="t">The value to bounce.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Hermite01(System.Single)">
            <summary>
            Returns a smooth value between 0 and 1 based on t.
            </summary>
            <param name="t">Position between 0 and 1.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.StackHermite01(System.Single,System.Int32)">
            <summary>
            Returns a smooth value between 0 and 1 based on t.
            </summary>
            <param name="t">Position between 0 and 1.</param>
            <param name="count">Number of interpolations to make.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.LerpUnclamped(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)">
            <summary>
            Returns an unclamped linear interpolation of two vectors.
            </summary>
            <param name="from">The first vector.</param>
            <param name="to">The second vector.</param>
            <param name="amount">The interpolation factor.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.LerpUnclamped(UnityEngine.Vector2,UnityEngine.Vector2,System.Single)">
            <summary>
            Returns an unclamped linear interpolation of two vectors.
            </summary>
            <param name="from">The first vector.</param>
            <param name="to">The second vector.</param>
            <param name="amount">The interpolation factor.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Bounce(System.Single)">
            <summary>
            Returns a value that bounces between 0 and 1 based on value.
            </summary>
            <param name="value">The value to bounce.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.EaseInElastic(System.Single,System.Single,System.Single)">
            <summary>
            Returns a value that eases in elasticly.
            </summary>
            <param name="value">The value to ease in elasticly.</param>
            <param name="amplitude">The amplitude.</param>
            <param name="length">The length.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Pow(UnityEngine.Vector3,System.Single)">
            <summary>
            Pows each element of the vector.
            </summary>
            <param name="v">The vector.</param>
            <param name="p">The power.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Abs(UnityEngine.Vector3)">
            <summary>
            Returns a Vector2 with each element set to their respective sign.
            </summary>
            <param name="v">The vector to sign.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Sign(UnityEngine.Vector3)">
            <summary>
            Returns a Vector3 with each element set to their respective sign.
            </summary>
            <param name="v">The vector to sign.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.EaseOutElastic(System.Single,System.Single,System.Single)">
            <summary>
            Returns a value that eases out elasticly.
            </summary>
            <param name="value">The value to ease out elasticly.</param>
            <param name="amplitude">The amplitude.</param>
            <param name="length">The length.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.EaseInOut(System.Single)">
            <summary>
            Returns a smooth value betweeen that peaks at t=0.5 and then comes back down again.
            </summary>
            <param name="t">A value between 0 and 1.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Clamp(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">
            <summary>
            Clamps the value of a Vector3.
            </summary>
            <param name="value">The vector to clamp.</param>
            <param name="min">The min value.</param>
            <param name="max">The max value.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Clamp(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2)">
            <summary>
            Clamps the value of a Vector2.
            </summary>
            <param name="value">The vector to clamp.</param>
            <param name="min">The min value.</param>
            <param name="max">The max value.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.ComputeByteArrayHash(System.Byte[])">
            <summary>
            Computes a hash for a byte array.
            </summary>
            <param name="data">The byte array.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.InterpolatePoints(UnityEngine.Vector3[],System.Single)">
            <summary>
            Gives a smooth path between a collection of points.
            </summary>
            <param name="path">The collection of point.</param>
            <param name="t">The current position in the path. 0 is at the start of the path, 1 is at the end of the path.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.LineIntersectsLine(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2@)">
            <summary>
            Checks if two given lines intersect with one another and returns the intersection point (if
            any) in an out parameter.
            Source: http://stackoverflow.com/questions/3746274/line-intersection-with-aabb-rectangle.
            Edited to implement Cohen-Sutherland type pruning for efficiency.
            </summary>
            <param name="a1">Starting point of line a.</param>
            <param name="a2">Ending point of line a.</param>
            <param name="b1">Starting point of line b.</param>
            <param name="b2">Ending point of line b.</param>
            <param name="intersection">
            The out parameter which contains the intersection point if there was any.
            </param>
            <returns>True if the two lines intersect, otherwise false.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.InfiniteLineIntersect(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Vector2)">
            <summary>
            Returns the collision point between two infinite lines.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.LineDistToPlane(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">
            <summary>
            Distance from line to plane.
            </summary>
            <param name="planeOrigin">Position of the plane.</param>
            <param name="planeNormal">Surface normal of the plane.</param>
            <param name="lineOrigin">Origin of the line.</param>
            <param name="lineDirectionNormalized">Line direction normal.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.RayDistToPlane(UnityEngine.Ray,UnityEngine.Plane)">
            <summary>
            Distance from ray to plane.
            </summary>
            <param name="ray">The ray.</param>
            <param name="plane">The plane.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.RotatePoint(UnityEngine.Vector2,System.Single)">
            <summary>
            Rotates a Vector2 by an angle.
            </summary>
            <param name="point">The point to rotate.</param>
            <param name="degrees">The angle to rotate.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.RotatePoint(UnityEngine.Vector2,UnityEngine.Vector2,System.Single)">
            <summary>
            Rotates a Vector2 around a point by an angle..
            </summary>
            <param name="point">The point to rotate.</param>
            <param name="around">The point to rotate around.</param>
            <param name="degrees">The angle to rotate.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.SmoothStep(System.Single,System.Single,System.Single)">
            <summary>
            Interpolates t between a and b to a value between 0 and 1 using a Hermite polynomial.
            </summary>
            <param name="a">The first value.</param>
            <param name="b">The second value.</param>
            <param name="t">The position value.</param>
            <returns>A smoothed value between 0 and 1.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.LinearStep(System.Single,System.Single,System.Single)">
            <summary>
            Interpolates t between a and b to a value between 0 and 1.
            </summary>
            <param name="a">The first value.</param>
            <param name="b">The second value.</param>
            <param name="t">The position value.</param>
            <returns>Linear value between 0 and 1.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Wrap(System.Double,System.Double,System.Double)">
            <summary>
            Wraps a value between min and max.
            </summary>
            <param name="value">The value to wrap.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Wrap(System.Single,System.Single,System.Single)">
            <summary>
            Wraps a value between min and max.
            </summary>
            <param name="value">The value to wrap.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.Wrap(System.Int32,System.Int32,System.Int32)">
            <summary>
            Wraps a value between min and max.
            </summary>
            <param name="value">The value to wrap.</param>
            <param name="min">The minimum value.</param>
            <param name="max">The maximum value.</param>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.RoundBasedOnMinimumDifference(System.Double,System.Double)">
            <summary>
            Rounds a number based on a mininum difference.
            </summary>
            <param name="valueToRound">The value to round.</param>
            <param name="minDifference">The min difference.</param>
            <returns>The rounded value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.DiscardLeastSignificantDecimal(System.Double)">
            <summary>
            Discards the least significant demicals.
            </summary>
            <param name="v">The value of insignificant decimals.</param>
            <returns>Value with significant decimals.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MathUtilities.ClampWrapAngle(System.Single,System.Single,System.Single)">
            <summary>
            Clamps and wraps an angle between two values.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.MemberAliasFieldInfo">
            <summary>
            Provides a methods of representing imaginary fields which are unique to serialization.
            <para />
            We aggregate the FieldInfo associated with this member and return a mangled form of the name.
            </summary>
            <seealso cref="T:System.Reflection.FieldInfo" />
        </member>
        <member name="F:Sirenix.Utilities.MemberAliasFieldInfo.FAKE_NAME_SEPARATOR_STRING">
            <summary>
            The default fake name separator string.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.#ctor(System.Reflection.FieldInfo,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasFieldInfo"/> class.
            </summary>
            <param name="field">The field to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.#ctor(System.Reflection.FieldInfo,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasFieldInfo"/> class.
            </summary>
            <param name="field">The field to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
            <param name="separatorString">The separator string to use.</param>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.AliasedField">
            <summary>
            Gets the aliased field.
            </summary>
            <value>
            The aliased field.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.Module">
            <summary>
            Gets the module in which the type that declares the member represented by the current <see cref="T:System.Reflection.MemberInfo" /> is defined.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.MetadataToken">
            <summary>
            Gets a value that identifies a metadata element.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.Name">
            <summary>
            Gets the name of the current member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.DeclaringType">
            <summary>
            Gets the class that declares this member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.ReflectedType">
            <summary>
            Gets the class object that was used to obtain this instance of MemberInfo.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.FieldType">
            <summary>
            Gets the type of the field.
            </summary>
            <value>
            The type of the field.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.FieldHandle">
            <summary>
            Gets a RuntimeFieldHandle, which is a handle to the internal metadata representation of a field.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasFieldInfo.Attributes">
            <summary>
            Gets the attributes.
            </summary>
            <value>
            The attributes.
            </value>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.GetCustomAttributes(System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of all custom attributes applied to this member.
            </summary>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array that contains all the custom attributes applied to this member, or an array with zero elements if no attributes are defined.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of custom attributes applied to this member and identified by <see cref="T:System.Type" />.
            </summary>
            <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array of custom attributes applied to this member, or an array with zero elements if no attributes assignable to <paramref name="attributeType" /> have been applied.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, indicates whether one or more attributes of the specified type or of its derived types is applied to this member.
            </summary>
            <param name="attributeType">The type of custom attribute to search for. The search includes derived types.</param>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            True if one or more instances of <paramref name="attributeType" /> or any of its derived types is applied to this member; otherwise, false.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.GetValue(System.Object)">
            <summary>
            Gets the value of the field.
            </summary>
            <param name="obj">The object instance to get the value from.</param>
            <returns>The value of the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasFieldInfo.SetValue(System.Object,System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Globalization.CultureInfo)">
            <summary>
            When overridden in a derived class, sets the value of the field supported by the given object.
            </summary>
            <param name="obj">The object whose field value will be set.</param>
            <param name="value">The value to assign to the field.</param>
            <param name="invokeAttr">A field of Binder that specifies the type of binding that is desired (for example, Binder.CreateInstance or Binder.ExactBinding).</param>
            <param name="binder">A set of properties that enables the binding, coercion of argument types, and invocation of members through reflection. If <paramref name="binder" /> is null, then Binder.DefaultBinding is used.</param>
            <param name="culture">The software preferences of a particular culture.</param>
        </member>
        <member name="T:Sirenix.Utilities.MemberAliasMethodInfo">
            <summary>
            Provides a methods of representing aliased methods.
            <para />
            In this case, what we're representing is a method on a parent class with the same name.
            <para />
            We aggregate the MethodInfo associated with this member and return a mangled form of the name.
            The name that we return is "parentname+methodName".
            </summary>
            <seealso cref="T:System.Reflection.FieldInfo" />
        </member>
        <member name="F:Sirenix.Utilities.MemberAliasMethodInfo.FAKE_NAME_SEPARATOR_STRING">
            <summary>
            The default fake name separator string.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.#ctor(System.Reflection.MethodInfo,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasMethodInfo"/> class.
            </summary>
            <param name="method">The method to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.#ctor(System.Reflection.MethodInfo,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasMethodInfo"/> class.
            </summary>
            <param name="method">The method to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
            <param name="separatorString">The separator string to use.</param>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.AliasedMethod">
            <summary>
            Gets the aliased method.
            </summary>
            <value>
            The aliased method.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.ReturnTypeCustomAttributes">
            <summary>
            Gets the custom attributes for the return type.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.MethodHandle">
            <summary>
            Gets a handle to the internal metadata representation of a method.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.Attributes">
            <summary>
            Gets the attributes associated with this method.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.DeclaringType">
            <summary>
            Gets the class that declares this member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.Name">
            <summary>
            Gets the name of the current member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasMethodInfo.ReflectedType">
            <summary>
            Gets the class object that was used to obtain this instance of MemberInfo.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.GetBaseDefinition">
            <summary>
            When overridden in a derived class, returns the MethodInfo object for the method on the direct or indirect base class in which the method represented by this instance was first declared.
            </summary>
            <returns>
            A MethodInfo object for the first implementation of this method.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.GetCustomAttributes(System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of all custom attributes applied to this member.
            </summary>
            <param name="inherit">true to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array that contains all the custom attributes applied to this member, or an array with zero elements if no attributes are defined.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of custom attributes applied to this member and identified by <see cref="T:System.Type" />.
            </summary>
            <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
            <param name="inherit">true to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array of custom attributes applied to this member, or an array with zero elements if no attributes assignable to <paramref name="attributeType" /> have been applied.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.GetMethodImplementationFlags">
            <summary>
            When overridden in a derived class, returns the <see cref="T:System.Reflection.MethodImplAttributes" /> flags.
            </summary>
            <returns>
            The MethodImplAttributes flags.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.GetParameters">
            <summary>
            When overridden in a derived class, gets the parameters of the specified method or constructor.
            </summary>
            <returns>
            An array of type ParameterInfo containing information that matches the signature of the method (or constructor) reflected by this MethodBase instance.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.Invoke(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            When overridden in a derived class, invokes the reflected method or constructor with the given parameters.
            </summary>
            <param name="obj">The object on which to invoke the method or constructor. If a method is static, this argument is ignored. If a constructor is static, this argument must be null or an instance of the class that defines the constructor.</param>
            <param name="invokeAttr">A bitmask that is a combination of 0 or more bit flags from <see cref="T:System.Reflection.BindingFlags" />. If <paramref name="binder" /> is null, this parameter is assigned the value <see cref="F:System.Reflection.BindingFlags.Default" />; thus, whatever you pass in is ignored.</param>
            <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of MemberInfo objects via reflection. If <paramref name="binder" /> is null, the default binder is used.</param>
            <param name="parameters">An argument list for the invoked method or constructor. This is an array of objects with the same number, order, and type as the parameters of the method or constructor to be invoked. If there are no parameters, this should be null.If the method or constructor represented by this instance takes a ByRef parameter, there is no special attribute required for that parameter in order to invoke the method or constructor using this function. Any object in this array that is not explicitly initialized with a value will contain the default value for that object type. For reference-type elements, this value is null. For value-type elements, this value is 0, 0.0, or false, depending on the specific element type.</param>
            <param name="culture">An instance of CultureInfo used to govern the coercion of types. If this is null, the CultureInfo for the current thread is used. (This is necessary to convert a String that represents 1000 to a Double value, for example, since 1000 is represented differently by different cultures.)</param>
            <returns>
            An Object containing the return value of the invoked method, or null in the case of a constructor, or null if the method's return type is void. Before calling the method or constructor, Invoke checks to see if the user has access permission and verifies that the parameters are valid.CautionElements of the <paramref name="parameters" /> array that represent parameters declared with the ref or out keyword may also be modified.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasMethodInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, indicates whether one or more attributes of the specified type or of its derived types is applied to this member.
            </summary>
            <param name="attributeType">The type of custom attribute to search for. The search includes derived types.</param>
            <param name="inherit">true to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            true if one or more instances of <paramref name="attributeType" /> or any of its derived types is applied to this member; otherwise, false.
            </returns>
        </member>
        <member name="T:Sirenix.Utilities.MemberAliasPropertyInfo">
            <summary>
            Provides a methods of representing imaginary properties which are unique to serialization.
            <para />
            We aggregate the PropertyInfo associated with this member and return a mangled form of the name.
            </summary>
            <seealso cref="T:System.Reflection.FieldInfo" />
        </member>
        <member name="F:Sirenix.Utilities.MemberAliasPropertyInfo.FakeNameSeparatorString">
            <summary>
            The default fake name separator string.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.#ctor(System.Reflection.PropertyInfo,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasPropertyInfo"/> class.
            </summary>
            <param name="prop">The property to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.#ctor(System.Reflection.PropertyInfo,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberAliasPropertyInfo"/> class.
            </summary>
            <param name="prop">The property to alias.</param>
            <param name="namePrefix">The name prefix to use.</param>
            <param name="separatorString">The separator string to use.</param>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.AliasedProperty">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.Module">
            <summary>
            Gets the module in which the type that declares the member represented by the current <see cref="T:System.Reflection.MemberInfo" /> is defined.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.MetadataToken">
            <summary>
            Gets a value that identifies a metadata element.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.Name">
            <summary>
            Gets the name of the current member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.DeclaringType">
            <summary>
            Gets the class that declares this member.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.ReflectedType">
            <summary>
            Gets the class object that was used to obtain this instance of MemberInfo.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.PropertyType">
            <summary>
            Gets the type of the property.
            </summary>
            <value>
            The type of the property.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.Attributes">
            <summary>
            Gets the attributes.
            </summary>
            <value>
            The attributes.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.CanRead">
            <summary>
            Gets a value indicating whether this instance can read.
            </summary>
            <value>
              <c>true</c> if this instance can read; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.MemberAliasPropertyInfo.CanWrite">
            <summary>
            Gets a value indicating whether this instance can write.
            </summary>
            <value>
              <c>true</c> if this instance can write; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetCustomAttributes(System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of all custom attributes applied to this member.
            </summary>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array that contains all the custom attributes applied to this member, or an array with zero elements if no attributes are defined.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, returns an array of custom attributes applied to this member and identified by <see cref="T:System.Type" />.
            </summary>
            <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            An array of custom attributes applied to this member, or an array with zero elements if no attributes assignable to <paramref name="attributeType" /> have been applied.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.IsDefined(System.Type,System.Boolean)">
            <summary>
            When overridden in a derived class, indicates whether one or more attributes of the specified type or of its derived types is applied to this member.
            </summary>
            <param name="attributeType">The type of custom attribute to search for. The search includes derived types.</param>
            <param name="inherit">True to search this member's inheritance chain to find the attributes; otherwise, false. This parameter is ignored for properties and events; see Remarks.</param>
            <returns>
            True if one or more instances of <paramref name="attributeType" /> or any of its derived types is applied to this member; otherwise, false.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetAccessors(System.Boolean)">
            <summary>
            Returns an array whose elements reflect the public and, if specified, non-public get, set, and other accessors of the property reflected by the current instance.
            </summary>
            <param name="nonPublic">Indicates whether non-public methods should be returned in the MethodInfo array. true if non-public methods are to be included; otherwise, false.</param>
            <returns>
            An array of <see cref="T:System.Reflection.MethodInfo" /> objects whose elements reflect the get, set, and other accessors of the property reflected by the current instance. If <paramref name="nonPublic" /> is true, this array contains public and non-public get, set, and other accessors. If <paramref name="nonPublic" /> is false, this array contains only public get, set, and other accessors. If no accessors with the specified visibility are found, this method returns an array with zero (0) elements.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetGetMethod(System.Boolean)">
            <summary>
            When overridden in a derived class, returns the public or non-public get accessor for this property.
            </summary>
            <param name="nonPublic">Indicates whether a non-public get accessor should be returned. true if a non-public accessor is to be returned; otherwise, false.</param>
            <returns>
            A MethodInfo object representing the get accessor for this property, if <paramref name="nonPublic" /> is true. Returns null if <paramref name="nonPublic" /> is false and the get accessor is non-public, or if <paramref name="nonPublic" /> is true but no get accessors exist.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetIndexParameters">
            <summary>
            Gets the index parameters of the property.
            </summary>
            <returns>The index parameters of the property.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetSetMethod(System.Boolean)">
            <summary>
            When overridden in a derived class, returns the set accessor for this property.
            </summary>
            <param name="nonPublic">Indicates whether the accessor should be returned if it is non-public. true if a non-public accessor is to be returned; otherwise, false.</param>
            <returns>
            Value Condition A <see cref="T:System.Reflection.MethodInfo" /> object representing the Set method for this property. The set accessor is public.-or- <paramref name="nonPublic" /> is true and the set accessor is non-public. null<paramref name="nonPublic" /> is true, but the property is read-only.-or- <paramref name="nonPublic" /> is false and the set accessor is non-public.-or- There is no set accessor.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.GetValue(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            Gets the value of the property on the given instance.
            </summary>
            <param name="obj">The object to invoke the getter on.</param>
            <param name="invokeAttr">The <see cref="T:System.Reflection.BindingFlags"/> to invoke with.</param>
            <param name="binder">The binder to use.</param>
            <param name="index">The indices to use.</param>
            <param name="culture">The culture to use.</param>
            <returns>The value of the property on the given instance.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberAliasPropertyInfo.SetValue(System.Object,System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
            <summary>
            Sets the value of the property on the given instance.
            </summary>
            <param name="obj">The object to set the value on.</param>
            <param name="value">The value to set.</param>
            <param name="invokeAttr">The <see cref="T:System.Reflection.BindingFlags"/> to invoke with.</param>
            <param name="binder">The binder to use.</param>
            <param name="index">The indices to use.</param>
            <param name="culture">The culture to use.</param>
        </member>
        <member name="T:Sirenix.Utilities.MultiDimArrayUtilities">
            <summary>
            Contains utilities for operating on arrays multi-dimentional arrays.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.InsertOneColumnLeft``1(``0[0:,0:],System.Int32)">
            <summary>
            Inserts one column left of the specified column index.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="columnIndex">Index of the column.</param>
            <param name="array">The array.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.InsertOneColumnRight``1(``0[0:,0:],System.Int32)">
            <summary>
            Inserts one column right of the specified column index.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="columnIndex">Index of the column.</param>
            <param name="arr">The arr.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.InsertOneRowAbove``1(``0[0:,0:],System.Int32)">
            <summary>
            Inserts one row above the specified row index.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="rowIndex">The row index.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.InsertOneRowBelow``1(``0[0:,0:],System.Int32)">
            <summary>
            Inserts one row below the specified row index.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="rowIndex">Index of the row.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.DuplicateColumn``1(``0[0:,0:],System.Int32)">
            <summary>
            Duplicates the column.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="columnIndex">Index of the column.</param>
            <param name="array">The array.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.DuplicateRow``1(``0[0:,0:],System.Int32)">
            <summary>
            Duplicates the row.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="rowIndex">Index of the row.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.MoveColumn``1(``0[0:,0:],System.Int32,System.Int32)">
            <summary>
            Moves a column.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="fromColumn">From column.</param>
            <param name="toColumn">To column.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.MoveRow``1(``0[0:,0:],System.Int32,System.Int32)">
            <summary>
            Moves a row.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="fromRow">From row.</param>
            <param name="toRow">To row.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.DeleteColumn``1(``0[0:,0:],System.Int32)">
            <summary>
            Deletes a column.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="columnIndex">Index of the column.</param>
        </member>
        <member name="M:Sirenix.Utilities.MultiDimArrayUtilities.DeleteRow``1(``0[0:,0:],System.Int32)">
            <summary>
            Deletes the row.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="array">The array.</param>
            <param name="rowIndex">Index of the row.</param>
        </member>
        <member name="T:Sirenix.Utilities.PersistentAssemblyAttribute">
            <summary>
            Indicates a persistent assembly.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.ReferenceEqualityComparer`1">
            <summary>
            Compares objects by reference only, ignoring equality operators completely. This is used by the property tree reference dictionaries to keep track of references.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.ReferenceEqualityComparer`1.Default">
            <summary>
            A default, cached instance of this generic variant of the reference equality comparer.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ReferenceEqualityComparer`1.Equals(`0,`0)">
            <summary>
            Returns true if the object references are equal.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.ReferenceEqualityComparer`1.GetHashCode(`0)">
            <summary>
            Returns the result of the object's own GetHashCode method.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.SirenixAssetPaths">
            <summary>
            Paths to Sirenix assets.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.OdinPath">
            <summary>
            Path to Odin Inspector folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.SirenixAssetsPath">
            <summary>
            Path to Sirenix assets folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.SirenixPluginPath">
            <summary>
            Path to Sirenix folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.SirenixAssembliesPath">
            <summary>
            Path to Sirenix assemblies.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.OdinResourcesPath">
            <summary>
            Path to Odin Inspector resources folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.OdinEditorConfigsPath">
            <summary>
            Path to Odin Inspector configuration folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.OdinResourcesConfigsPath">
            <summary>
            Path to Odin Inspector resources configuration folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.SirenixAssetPaths.OdinTempPath">
            <summary>
            Path to Odin Inspector temporary folder.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.SirenixEditorConfigAttribute">
            <summary>
            <para>This attribute is used by classes deriving from GlobalConfig and specifies the menu item path for the preference window and the asset path for the generated config file.</para>
            <para>The scriptable object created will be located at the OdinEditorConfigs path unless other is specified.</para>
            <para>Classes implementing this attribute will be part of the Odin Preferences window.</para>
            </summary>
            <seealso cref="T:Sirenix.Utilities.SirenixGlobalConfigAttribute"/>
        </member>
        <member name="M:Sirenix.Utilities.SirenixEditorConfigAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.SirenixEditorConfigAttribute"/> class.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.SirenixGlobalConfigAttribute">
            <summary>
            <para>This attribute is used by classes deriving from GlobalConfig and specifies the menu item path for the preference window and the asset path for the generated config file.</para>
            <para>The scriptable object created will be located at the OdinResourcesConigs path unless other is specified.</para>
            <para>Classes implementing this attribute will be part of the Odin Preferences window.</para>
            </summary>
            <seealso cref="T:Sirenix.Utilities.SirenixEditorConfigAttribute"/>
        </member>
        <member name="M:Sirenix.Utilities.SirenixGlobalConfigAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.SirenixGlobalConfigAttribute"/> class.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.StringUtilities">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.StringUtilities.NicifyByteSize(System.Int32,System.Int32)">
            <summary>
            Not yet documented.
            </summary>
            <param name="bytes">Not yet documented.</param>
            <param name="decimals">Not yet documented.</param>
            <returns>Not yet documented.</returns>
        </member>
        <member name="M:Sirenix.Utilities.StringUtilities.NumberAwareStringCompare(System.String,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Compares two strings in a number-aware manner, IE, "[2] Foo" is considered to come before "[10] Bar".
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.UnityVersion">
            <summary>
            Utility class indicating current Unity version.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.UnityVersion.IsVersionOrGreater(System.Int32,System.Int32)">
            <summary>
            Tests current Unity version is equal or greater.
            </summary>
            <param name="major">Minimum major version.</param>
            <param name="minor">Minimum minor version.</param>
            <returns><c>true</c> if the current Unity version is greater. Otherwise <c>false</c>.</returns>
        </member>
        <member name="F:Sirenix.Utilities.UnityVersion.Major">
            <summary>
            The current Unity version major.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.UnityVersion.Minor">
            <summary>
            The current Unity version minor.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Unsafe.UnsafeUtilities">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StructArrayFromBytes``1(System.Byte[],System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StructArrayFromBytes``1(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StructArrayToBytes``1(``0[])">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StructArrayToBytes``1(``0[],System.Byte[]@,System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StringFromBytes(System.Byte[],System.Int32,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.StringToBytes(System.Byte[],System.String,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Unsafe.UnsafeUtilities.MemoryCopy(System.Object,System.Object,System.Int32,System.Int32,System.Int32)">
            <summary>
            Not yet documented.
            </summary>
        </member>
    </members>
</doc>
