using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using Fusion.Photon.Realtime;
using Modules.Core;
using Newtonsoft.Json.Linq;
using Unity.XR.Oculus;
using UnityEditor;
using UnityEditor.XR.Management;
using UnityEngine;
using UnityEngine.XR.Management;
using Debug = UnityEngine.Debug;

// ============================================================================================================
// RemioBuildWindow
// ============================================================================================================
[InitializeOnLoad]
public class YoshiBuildWindow : EditorWindow
{
    // ============================================================================================================
    // PlatformVersion
    // ============================================================================================================
    private enum PlatformVersion
    {
        Yoshi,
        Standalone
    }

    private const string devBackendFirebase = "-ezkj6ouymq-uc.a.run.app";
    private const string prodBackendFirebase = "-kryohe5mxq-uc.a.run.app";
    private static string oculusAppId = "26317184997880598";
    private const string oculusAppIdProd = "26317184997880598";
    private const string oculusAppIdDev = "26317184997880598";
    private const string packageName = "com.remio.beastcraft";
    private const string displayName = "Animal Blocks";

    private PhotonAppSettings settings;
    // ============================================================================================================
    // Version override
    // ============================================================================================================
    private static PlatformVersion platformVersion;

    private string version; // Private field to store version
    private bool isInitialized;
    private ApiType apiType;

    [MenuItem("Remio/Yoshi Build window")]
    private static void Init()
    {
        // Get existing open window or if none, make a new one:
        var window = (YoshiBuildWindow)GetWindow(typeof(YoshiBuildWindow));
        window.titleContent = new GUIContent("Yoshi build system");

        window.Show();
    }

    private void OnGUI()
    {
        // Versioning
        GUILayout.Label("Versioning", EditorStyles.boldLabel);
        EditorGUILayout.HelpBox("Settings below can be used to modify build version locally." +
                                " It modifies version for the current build platform.", MessageType.Info);

        if (!isInitialized)
        {
            var appConfig = LoadAssetOfType<AppConfig>();
            version = appConfig.GameVersion; // Only get the version initially
            apiType = appConfig.ApiType;
            isInitialized = true;
        }

        version = EditorGUILayout.TextField("Build version", version);
        apiType = (ApiType)EditorGUILayout.EnumPopup("Api endpoint", apiType);

        if (GUILayout.Button("Get Backend Version"))
        {
            GetBackendVersion();
        }

        if (GUILayout.Button("Set Current Version"))
        {
            SetCurrentVersion();
        }

        if (GUILayout.Button("Sync Backend Version"))
        {
            GetBackendVersion();
            SetCurrentVersion();
        }
    }

    private void GetBackendVersion()
    {
        var bundleVersion = string.Empty; // Default value assignment
        var bundleVersionCode = 0; // Default value assignment
        GetCurrentBundleVersion(apiType, out bundleVersion, out bundleVersionCode, () => { version = $"{bundleVersion}.{bundleVersionCode}"; });
    }

    private void SetCurrentVersion()
    {
        var bundleVersionCode = -1;
        var bundleVersion = string.Empty;
        GetVersion(version, out bundleVersion, out bundleVersionCode);
        ApplyCurrentBundleVersion(apiType, bundleVersion, bundleVersionCode);
    }

    private void GetVersion(string version, out string bundleVersion, out int bundleVersionCode)
    {
        var versionSplit = version.Split('.');
        bundleVersion = string.Format("{0}.{1}", versionSplit[0], versionSplit[1]);
        bundleVersionCode = int.Parse(versionSplit[2]);
    }

    public static string GetArg(string name)
    {
        var args = Environment.GetCommandLineArgs();
        for (var i = 0; i < args.Length; i++)
        {
            if (args[i] == name && args.Length > i + 1)
            {
                return args[i + 1];
            }
        }

        return null;
    }

    private static void SetSplashScreen()
    {
        if (!IsOculusLoaderActive())
        {
            return;
        }

        // Load the XR General Settings for the current build target
        var xrGeneralSettings =
            XRGeneralSettingsPerBuildTarget.XRGeneralSettingsForBuildTarget(BuildTargetGroup.Android);

        if (xrGeneralSettings == null)
        {
            Debug.LogError("XR General Settings not found for the Android build target.");
            return;
        }

        // Load the XR Manager Settings
        var xrManagerSettings = xrGeneralSettings.AssignedSettings;

        if (xrManagerSettings == null)
        {
            Debug.LogError("XR Manager Settings not found.");
            return;
        }

        // Find the Oculus loader
        var oculusLoader = xrManagerSettings.activeLoaders[0] as OculusLoader;
        if (oculusLoader == null)
        {
            Debug.LogError("Oculus Loader not found.");
            return;
        }

        // Access the Oculus settings
        var oculusSettings = oculusLoader.GetSettings();
        if (oculusSettings == null)
        {
            Debug.LogError("Oculus Settings not found.");
            return;
        }

        // Change the splash screen
        // oculusSettings.SystemSplashScreen = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/Framework/Remio.XR/Content/Textures/beastcraft_splash.png");

        // Mark the settings as dirty to ensure they are saved
        EditorUtility.SetDirty(oculusSettings);
        AssetDatabase.SaveAssets();

        // Debug.Log("Oculus splash screen updated successfully.");
    }

    private static bool IsOculusLoaderActive()
    {
#if UNITY_ANDROID
        var instance = XRGeneralSettings.Instance;
        return instance != null && instance.AssignedSettings.activeLoaders.Count > 0 && instance.AssignedSettings.activeLoaders[0] is OculusLoader;
#else
        return false;
#endif
    }

    public static bool HasArg(string name)
    {
        var args = Environment.GetCommandLineArgs();
        for (var i = 0; i < args.Length; i++)
        {
            if (args[i].Contains(name))
            {
                return true;
            }
        }

        return false;
    }

    // ============================================================================================================

    public static void CommandLineBuild()
    {
        try
        {
            if (HasArg("-apiEndpoint"))
            {
                var apiArg = GetArg("-apiEndpoint").ToLower();
                Enum.TryParse(apiArg, true, out ApiType apiType);

                if (HasArg("-platformVersion"))
                {
                    var platform = GetArg("-platformVersion").ToLower();
                    if (platform == "android")
                    {
                        oculusAppId = apiType == ApiType.Dev ? oculusAppIdDev : oculusAppIdProd;
                        Oculus.Platform.PlatformSettings.AppID = Oculus.Platform.PlatformSettings.MobileAppID = oculusAppId;
                        EditorUserBuildSettings.androidCreateSymbols = AndroidCreateSymbols.Debugging; // Create symbols for Android builds

                        PlayerSettings.productName = displayName;
                        PlayerSettings.SetApplicationIdentifier(EditorUserBuildSettings.selectedBuildTargetGroup, packageName);

                        if (HasArg("-appIdentifier"))
                        {
                            var appIdentifier = GetArg("-appIdentifier");
                            PlayerSettings.SetApplicationIdentifier(EditorUserBuildSettings.selectedBuildTargetGroup, appIdentifier);
                        }

                        // Check if we need to split bundle i.e. create an APK (main) and OBB (files)
                        // We need to split the APK for releases because of size restrictions
                        // We don't need to split the APK for development because it's easier to side-load
                        if (HasArg("-splitBundle"))
                        {
                            PlayerSettings.Android.useAPKExpansionFiles = true;
                        }
                        else
                        {
                            PlayerSettings.Android.useAPKExpansionFiles = false;
                        }

                        Build(apiType);
                    }
                    else if (platform == "standalone")
                    {
                        platformVersion = PlatformVersion.Standalone;

                        Build(apiType);
                    }
                    else
                    {
                        throw new Exception("-platformVersion argument is not recognized. Aborting.");
                    }
                }
                else
                {
                    throw new Exception("-platformVersion argument is missing. Aborting.");
                }
            }
            else
            {
                throw new Exception("-apiEndpoint argument is not recognized. Aborting.");
            }
        }
        catch (Exception e)
        {
            Debug.LogError(string.Format("An exception occured. Message: \n {0}\n StackTrace:\n {1}\n", e.Message, e.StackTrace));
            EditorApplication.Exit(1);
        }

        EditorApplication.Exit(0);
    }

    public class Scene
    {
        public string path { get; set; }
    }
    // ============================================================================================================

    private static void Build(ApiType apiType, bool ignoreVersionCheck = false)
    {
#if UNITY_ANDROID
        AssetDatabase.SaveAssets();
#endif
        var bundleVersion = PlayerSettings.bundleVersion;
        var bundleVersionCode = PlayerSettings.Android.bundleVersionCode;

        // Version check
        var versionChangedInBatchmode = false;

        if (HasArg("-setVersion"))
        {
            var version = GetArg("-setVersion");
            if (!string.IsNullOrEmpty(version))
            {
                var versionSplit = version.Split('.');
                bundleVersion = string.Format("{0}.{1}", versionSplit[0], versionSplit[1]);
                bundleVersionCode = int.Parse(versionSplit[2]);
                versionChangedInBatchmode = true;
            }
        }

        if (!versionChangedInBatchmode)
        {
            GetCurrentBundleVersion(apiType, out bundleVersion, out bundleVersionCode, null);
            if (HasArg("-incrementVersion"))
            {
                bundleVersionCode += 1;
            }

            versionChangedInBatchmode = true;
        }

        if (versionChangedInBatchmode)
            ApplyCurrentBundleVersion(apiType, bundleVersion, bundleVersionCode);

        MakeBuild(apiType, BuildTarget.Android, PlayerSettings.bundleVersion, PlayerSettings.Android.bundleVersionCode);
    }

    // ============================================================================================================

    internal static void MakeBuild(ApiType target, BuildTarget targetPlatform, string bundleVersion, int bundleVersionCode)
    {
        // Get filename.
        var path = string.Empty;
        Debug.Log("platformVersion: " + platformVersion);
        var buildName = string.Format("remio_{0}_{1}_{2}.{3}.apk", "yoshi", target.ToString().ToLower(), bundleVersion, bundleVersionCode);
        var directoryPath = EditorUtility.SaveFolderPanel("Choose location where to put built game", "", "");
        if (HasArg("-targetPath"))
        {
            // Get destination path
            directoryPath = GetArg("-targetPath");
        }

        path = Path.Combine(directoryPath, buildName);

        if (string.IsNullOrEmpty(path))
        {
            Debug.LogError("Path is empty");
            throw new Exception("Path is empty");
        }

        // Get all active scenes
        var editorScenes = new List<EditorBuildSettingsScene>(EditorBuildSettings.scenes);
        var enabledEditorScenes = new List<EditorBuildSettingsScene>();

        if (HasArg("-scenesToBuild"))
        {
            var scenesToBuildPaths = new List<string>(GetArg("-scenesToBuild").Split(','));

            for (var i = 0; i < editorScenes.Count; i++)
            {
                if (editorScenes[i] == null)
                {
                    throw new Exception("One of the scenes in build settings is empty or null. Aborting.");
                }

                if (!editorScenes[i].enabled)
                {
                    continue;
                }

                if (!scenesToBuildPaths.Contains(editorScenes[i].path))
                {
                    continue;
                }

                enabledEditorScenes.Add(editorScenes[i]);
            }
        }
        else
        {
            for (var i = 0; i < editorScenes.Count; i++)
            {
                if (editorScenes[i] == null)
                {
                    throw new Exception("One of the scenes in build settings is empty or null. Aborting.");
                }

                if (!editorScenes[i].enabled)
                {
                    continue;
                }

                enabledEditorScenes.Add(editorScenes[i]);
            }
        }

        // Sign keystore
        if (targetPlatform == BuildTarget.Android)
        {
            // Check keystore
            if (!File.Exists(PlayerSettings.Android.keystoreName))
            {
                if (HasArg("-keystorePath"))
                {
                    PlayerSettings.Android.keystoreName = GetArg("-keystorePath");
                }
                else
                {
                    Debug.LogError("-keystorePath missing. Aborting build.");
                    throw new Exception("-keystorePath missing. Aborting build.");
                }
            }

            Debug.Log("Applying android keystore passwords and keyalias name.");
            PlayerSettings.Android.keyaliasName = "remiomansion";
            PlayerSettings.Android.keystorePass = "remio1234";
            PlayerSettings.Android.keyaliasPass = "remio1234";
        }

        // Remove development flag unless dev build is set
        var currentBuildOptions = new BuildOptions();
        if (HasArg("-devBuild"))
        {
            Debug.LogError("DevBuild flag set, making a development build");
            currentBuildOptions = currentBuildOptions | BuildOptions.Development | BuildOptions.ConnectWithProfiler;
        }
        else
        {
            currentBuildOptions = currentBuildOptions & BuildOptions.Development;
        }

        Fusion.Editor.NetworkProjectConfigUtilities.RebuildPrefabTable();

        // Build player
        var report = BuildPipeline.BuildPlayer(enabledEditorScenes.ToArray(), path, targetPlatform, currentBuildOptions);

        // Build finished
        if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log("Build finished. Output path = " + report.summary.outputPath);
        }
        else
        {
            Debug.LogError("Build failed.");
            throw new Exception("Build failed.");
        }

        EditorUtility.ClearProgressBar();
    }

    // ============================================================================================================
    internal static void ApplyCurrentBundleVersion(ApiType type, string bundleVersion, int bundleVersionCode)
    {
        Debug.Log(string.Format("Applying current bundle version for {0} is {1}.{2}", type.ToString(), bundleVersion, bundleVersionCode));
        if (!string.IsNullOrEmpty(bundleVersion) && bundleVersionCode != -1)
        {
            if (bundleVersionCode <= 0 || bundleVersionCode >= 100000)
            {
                if (type == ApiType.Dev)
                {
                    Debug.LogError("Bundle version code cannot be <= 0 and >= 100000. Clamping " +
                                   "between these two values will happen automatically to prevent build failure.");
                    bundleVersionCode = Math.Clamp(bundleVersionCode, 1, 99999);
                }
                else
                {
                    throw new Exception("Bundle version code cannot be <= 0 and >= 100000. Abort build if not dev.");
                }
            }

            oculusAppId = type == ApiType.Dev ? oculusAppIdDev : oculusAppIdProd;

            PlayerSettings.bundleVersion = bundleVersion;
            PlayerSettings.Android.bundleVersionCode = bundleVersionCode;

            var photonAppSettings = Resources.Load<PhotonAppSettings>(nameof(PhotonAppSettings));
            photonAppSettings.AppSettings.AppVersion = bundleVersion;

            EditorUtility.SetDirty(photonAppSettings);
            AssetDatabase.SaveAssets();

            var appConfig = LoadAssetOfType<AppConfig>();
            appConfig.SetGameVersion(bundleVersion, bundleVersionCode);
            appConfig.SetApiType(type);
            appConfig.SetNetworkKeys();
            Oculus.Platform.PlatformSettings.AppID = Oculus.Platform.PlatformSettings.MobileAppID = oculusAppId;

            appConfig.SetFirebaseBaseUri(type == ApiType.Dev ? devBackendFirebase : prodBackendFirebase);

            SetSplashScreen();
        }
        else
        {
            Debug.Log("Either bundle version or bundle version code is not valid.");
            throw new Exception("Either bundle version or bundle version code is not valid.");
        }
    }

    private static string GetCommitHash()
    {
        var commitHash = Environment.GetEnvironmentVariable("CI_COMMIT_SHORT_SHA");

        return string.IsNullOrEmpty(commitHash) ? Guid.NewGuid().ToString()[..5] : commitHash;
    }

    private static T LoadAssetOfType<T>() where T : UnityEngine.Object
    {
        return AssetDatabase
            .FindAssets($"t:{typeof(T).Name}")
            .Select(AssetDatabase.GUIDToAssetPath)
            .Select(AssetDatabase.LoadAssetAtPath<T>)
            .FirstOrDefault();
    }

    // ============================================================================================================

    #region BackendUtilities

    internal static void UpdateBackendInternal(ApiType type, string platform, string combinedVersion)
    {
        var versionSplit = combinedVersion.Split('.');
        var bundleVersion = string.Format("{0}.{1}", versionSplit[0], versionSplit[1]);
        var bundleVersionCode = int.Parse(versionSplit[2]);

        Debug.Log(string.Format("Changing backend min version for {0} to {1}.{2}", type.ToString(), bundleVersion, bundleVersionCode));
        var request = WebRequest.Create(new Uri($"https://setappversion{(type == ApiType.Dev ? devBackendFirebase : prodBackendFirebase)}"));
        request.Method = "POST";
        var response = request.GetResponse();
        Debug.Log("Version update on the backend called. Outcome = " + ((HttpWebResponse)response).StatusDescription);
    }

    internal static void GetCurrentBundleVersion(ApiType type, out string bundleVersion, out int bundleVersionCode, System.Action finishedCallback)
    {
        Debug.Log(string.Format("Checking current bundle version for {0}", type.ToString()));

        bundleVersion = string.Empty;
        bundleVersionCode = -1;

        var request = WebRequest.Create(new Uri($"https://getappversion{(type == ApiType.Dev ? devBackendFirebase : prodBackendFirebase)}"));

        request.Method = "GET";
        var response = request.GetResponse();

        if (((HttpWebResponse)response).StatusCode == HttpStatusCode.OK)
        {
            var encoding = System.Text.Encoding.ASCII;
            using (var reader = new StreamReader(response.GetResponseStream(), encoding))
            {
                var responseObj = JObject.Parse(reader.ReadToEnd());
                var version = ((string)responseObj["version"]).Split('.');
                if (version.Length >= 1)
                {
                    bundleVersion = string.Format("{0}.{1}", version[0], version[1]);
                }

                if (version.Length > 2)
                {
                    bundleVersionCode = int.Parse(version[2]);
                }
                else
                {
                    bundleVersionCode = 0;
                }
            }
        }

        Debug.Log(string.Format("Current bundle version for {0} on backend is {1}.{2}", type.ToString(), bundleVersion, bundleVersionCode));
        finishedCallback?.Invoke();
    }

    #endregion
}