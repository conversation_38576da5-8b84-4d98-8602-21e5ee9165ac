#if UNITY_EDITOR
using UnityEditor;
using UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation;

namespace Game.Editor
{
    public class SetDeviceSimulatorFlyMode : UnityEditor.Editor
    {
        [MenuItem("Remio/Utilities/Set Fast Walk (Play Mode only)")]
        public static void SetFlyMode()
        {
            var xrDeviceSimulator = FindObjectOfType<XRDeviceSimulator>();
            if (xrDeviceSimulator != null)
            {
                xrDeviceSimulator.keyboardXTranslateSpeed = 1;
                xrDeviceSimulator.keyboardYTranslateSpeed = 1;
                xrDeviceSimulator.keyboardZTranslateSpeed = 1;
            }
        }

        [MenuItem("Remio/Utilities/Set Slow Walk (Play Mode only)")]
        public static void SetWalkMode()
        {
            var xrDeviceSimulator = FindObjectOfType<XRDeviceSimulator>();
            if (xrDeviceSimulator != null)
            {
                xrDeviceSimulator.keyboardXTranslateSpeed = 0.2f;
                xrDeviceSimulator.keyboardYTranslateSpeed = 0.2f;
                xrDeviceSimulator.keyboardZTranslateSpeed = 0.2f;
            }
        }
    }
}
#endif